---
type: "always_apply"
description: "Example description"
---
# 项目通用规范

## 项目结构规则
- **分层组织**：按功能或领域划分目录，遵循"关注点分离"原则
- **命名一致**：使用一致且描述性的目录和文件命名，反映其用途和内容
- **模块化**：相关功能放在同一模块，减少跨模块依赖
- **适当嵌套**：避免过深的目录嵌套，一般不超过3-4层
- **资源分类**：区分代码、资源、配置和测试文件
- **依赖管理**：集中管理依赖，避免多处声明
- **约定优先**：遵循语言或框架的标准项目结构约定

## 通用开发原则
- **MVP原则**：作最小代码和功能改动以完成用户需求，禁止自行猜测业务和数据逻辑增加复杂度，必要时向用户确认
- **深思审视**：禁止对更新后版本代码过度乐观（比如认为现在实现完美符合要求、完全解决问题）与夸赞（比如盲目认同用户从不反驳），永远慎重对代码可能的遗留问题
- **可测试性**：编写可测试的代码，组件应保持单一职责
- **DRY 原则**：避免重复代码，提取共用逻辑到单独的函数或类
- **代码简洁**：保持代码简洁明了，遵循 KISS 原则（保持简单直接）
- **命名规范**：使用描述性的变量、函数和类名，反映其用途和含义
- **注释文档**：为复杂逻辑添加注释，编写清晰的文档说明功能和用法
- **风格一致**：遵循项目或语言的官方风格指南和代码约定
- **利用生态**：优先使用成熟的库和工具，避免不必要的自定义实现
- **架构设计**：考虑代码的可维护性、可扩展性和性能需求
- **版本控制**：编写有意义的提交信息，保持逻辑相关的更改在同一提交中
- **异常处理**：正确处理边缘情况和错误，提供有用的错误信息 
- **数据驱动**： 不能为了跑通测试硬编码任何条件逻辑或者数据，让整体流程都按照数据本身定义来，以处理未来不同的数据输入。
- 禁止在代码中加入 emoji，除非用户要求
- 禁止转换原始数据语言


## 响应语言
- Always response in Chinese, but keep the original language for code and in/output data.
- 解释代码时始终附上最相关代码片段