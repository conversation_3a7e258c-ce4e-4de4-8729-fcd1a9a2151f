#!/usr/bin/env python3
"""
Enhanced Node-Only Path Description Generator
Generates improved step-by-step descriptions for node-only paths with aggregated condition choices
"""

import json
from collections import defaultdict

def load_json_file(filename):
    """Load JSON file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return None
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in {filename}: {e}")
        return None

def aggregate_condition_choices(full_paths, node_only_paths):
    """
    Aggregate all possible condition choices for each node-only path
    """
    # Create mapping from node signature to all possible condition choices
    node_signature_to_conditions = defaultdict(lambda: defaultdict(set))
    
    # Process full paths to collect all condition choices
    for full_path in full_paths['paths']:
        node_signature = full_path.get('path_signature_nodes_only')
        if not node_signature:
            continue
            
        # Extract condition choices from this path
        for condition in full_path.get('condition_sequence', []):
            step_num = condition['step']
            node_id = condition['node_id']
            question = condition['question']
            choice = condition['choice']
            
            # Store all choices for this step/question combination
            node_signature_to_conditions[node_signature][(step_num, node_id, question)].add(choice)
    
    return node_signature_to_conditions

def generate_step_format_description(path_data, aggregated_conditions):
    """
    Generate enhanced step-by-step format description
    """
    node_signature = path_data.get('path_signature_nodes_only')
    steps = path_data.get('steps', [])
    
    if not steps:
        return "No steps available"
    
    # Node type mapping for cleaner display
    type_mapping = {
        'start_node': 'Start',
        'intent_node': 'Intent',
        'info_collection_node': 'Info Collection',
        'condition_node': 'Condition',
        'info_expression_node': 'Info Expression'
    }
    
    description_lines = []
    description_lines.append("SOP Process Steps:")
    
    for i, step in enumerate(steps):
        step_num = step['step']
        node_type = step['type']
        title = step['title']
        action = step['action']
        
        # Special handling for the first step (always the same)
        if step_num == 1 and node_type == 'start_node':
            description_lines.append("1.Begin processing user request")
        else:
            # Format step header with type and title combined
            type_display = type_mapping.get(node_type, node_type)
            description_lines.append(f"{step_num}.{type_display} - {title}")
            
            # Check if action provides meaningful additional information
            should_add_action = True
            
            # Remove action if it's redundant with title
            if node_type == 'intent_node' and action.startswith("Identify user intent:") and title in action:
                should_add_action = False
            elif node_type == 'info_collection_node' and action.startswith("Collect user information"):
                should_add_action = False
            elif node_type == 'condition_node' and action.startswith("Evaluate condition:") and title in action:
                should_add_action = False
            elif node_type == 'info_expression_node' and action.startswith("Inform user:") and title in action:
                should_add_action = False
                
            if should_add_action:
                description_lines.append(f"  - Action: {action}")
        
        # Add condition choices if this is a condition node
        if node_type == "condition_node" and node_signature in aggregated_conditions:
            node_id = step['node_id']
            
            # Find all possible choices for this step
            for (agg_step, agg_node_id, question), choices in aggregated_conditions[node_signature].items():
                if agg_step == step_num and agg_node_id == node_id:
                    choices_list = sorted(list(choices))
                    if len(choices_list) == 1:
                        description_lines.append(f"  - Available choice: \"{choices_list[0]}\"")
                    else:
                        formatted_choices = '", "'.join(choices_list)
                        description_lines.append(f"  - Available choices: [\"{formatted_choices}\"]")
                    break
        
        # Add empty line between steps (except for the last step)
        if i < len(steps) - 1:
            description_lines.append("")
    
    return "\n".join(description_lines)

def main():
    print("Loading data files...")
    
    # Load both files
    full_paths = load_json_file('sop_paths_extracted_full.json')
    node_only_paths = load_json_file('sop_paths_extracted_nodes_only.json')
    
    if not full_paths or not node_only_paths:
        return
    
    print(f"Loaded {len(full_paths['paths'])} full paths and {len(node_only_paths['paths'])} node-only paths")
    
    # Aggregate condition choices
    print("Aggregating condition choices...")
    aggregated_conditions = aggregate_condition_choices(full_paths, node_only_paths)
    
    # Generate enhanced descriptions
    enhanced_paths = []
    
    for path_data in node_only_paths['paths']:
        path_id = path_data['path_id']
        print(f"Processing {path_id}...")
        
        enhanced_description = generate_step_format_description(path_data, aggregated_conditions)
        
        # Extract numeric ID from path_id (e.g., "path_0001" -> 1)
        numeric_id = int(path_id.split('_')[-1])
        
        enhanced_path = {
            "SOP_path_id": numeric_id,
            "SOP_path_description": enhanced_description
        }
        
        enhanced_paths.append(enhanced_path)
    
    # Save results with simplified structure
    output_data = {
        "SOP_paths": enhanced_paths
    }
    
    output_filename = 'sop_paths_enhanced_descriptions.json'
    with open(output_filename, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"\nGenerated enhanced descriptions saved to: {output_filename}")
    print(f"Total paths processed: {len(enhanced_paths)}")
    
    # Show a sample
    if enhanced_paths:
        print("\n" + "="*60)
        print("SAMPLE OUTPUT:")
        print("="*60)
        print(f"Path ID: {enhanced_paths[0]['SOP_path_id']}")
        print(f"Description:\n{enhanced_paths[0]['SOP_path_description']}")

if __name__ == "__main__":
    main() 