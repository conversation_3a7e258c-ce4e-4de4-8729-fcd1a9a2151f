#!/usr/bin/env python3
"""
Generate natural, concise user inputs based on extracted path information
基于提取的路径信息生成自然、简洁的用户输入
"""

import json
import sys
from typing import List, Dict
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from sop_core import create_llm
from langchain_core.messages import HumanMessage

class NaturalInputGenerator:
    def __init__(self):
        self.llm = None
        self.init_llm()
    
    def init_llm(self):
        """Initialize LLM"""
        try:
            self.llm = create_llm()
            print("✓ LLM initialized successfully")
        except Exception as e:
            print(f"Warning: LLM initialization failed - {e}")
            print("Will use fallback templates")
    
    def load_path_info(self, path_info_file: str) -> Dict:
        """Load extracted path information"""
        try:
            with open(path_info_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Path info file not found: {path_info_file}")
    
    def create_llm_prompt(self, step: Dict, context: Dict) -> str:
        """Create LLM prompt for generating user input based on path data"""
        node_type = step['node_type']
        node_title = step['node_title']
        condition = step.get('condition_trigger')
        requirements = step.get('input_requirements', {})

        # Get path context for better generation
        path_id = context.get('path_id', '')
        current_step = step['step']

        # Build conversation context from previous steps
        conversation_context = self._build_conversation_context(step, context)

        base_prompt = f"""You are generating a realistic user response for a 2FA support conversation.

CONVERSATION CONTEXT:
- This is step {current_step} in path {path_id}
- User is seeking help with 2FA issues
- User should respond naturally like a real frustrated customer

{conversation_context}

CURRENT SITUATION:
- Node type: {node_type}
- Agent question/prompt: "{node_title}"
"""

        if condition:
            # Get all available conditions for this node
            all_conditions = requirements.get('available_conditions', [])
            target_condition = condition

            if all_conditions:
                # Find which condition index we need to trigger
                condition_index = None
                for i, cond in enumerate(all_conditions):
                    if cond == target_condition:
                        condition_index = i
                        break

                base_prompt += f"""
CRITICAL REQUIREMENT:
This is a condition node with {len(all_conditions)} possible responses:
"""
                for i, cond in enumerate(all_conditions):
                    marker = "← MUST TRIGGER THIS" if i == condition_index else ""
                    base_prompt += f"  Option {i}: \"{cond}\" {marker}\n"

                base_prompt += f"""
The user's response must clearly trigger option {condition_index}: "{target_condition}"

IMPORTANT: The user response should naturally express the meaning of "{target_condition}"
without directly quoting it. Think about what a real user would say to convey this intent.
"""
            else:
                base_prompt += f"""
REQUIREMENT: The user response must convey the intent: "{target_condition}"
"""

        # Add context about what type of response is needed
        if node_type == 'info_collection_node':
            base_prompt += "\nThe user should provide the requested information naturally."
        elif node_type == 'condition_node':
            base_prompt += f"\nThe user should respond in a way that clearly indicates: {target_condition}"
        elif node_type == 'info_expression_node':
            base_prompt += "\nThe user should acknowledge or respond to the information provided."

        base_prompt += f"""

GENERATION REQUIREMENTS:
- Sound like a real frustrated customer, not a perfect AI response
- Length: 8-20 words for natural conversation flow
- Include some emotion or context to make it realistic
- Be clear enough that the intent can be understood

Generate ONLY the user response, nothing else:"""

        return base_prompt

    def _build_conversation_context(self, current_step: Dict, context: Dict) -> str:
        """Build conversation context from previous steps to maintain consistency"""
        current_step_num = current_step['step']

        if current_step_num <= 3:
            return ""  # No previous context for early steps

        # Get all steps from the path data
        path_data = context.get('path_data', {})
        all_steps = path_data.get('steps', [])

        # Find previous steps that had user input
        previous_context = []
        key_context_info = []

        for step in all_steps:
            if step['step'] < current_step_num and step.get('condition_trigger'):
                step_info = f"Step {step['step']}: User expressed '{step['condition_trigger']}'"
                previous_context.append(step_info)

                # Extract key context information for consistency
                condition = step['condition_trigger']
                if 'not the account owner' in condition.lower():
                    key_context_info.append("User established this is NOT their account (belongs to someone else)")
                elif 'boss' in condition.lower() or 'employer' in condition.lower():
                    key_context_info.append("User is helping someone else with their account")

        if previous_context:
            context_str = "PREVIOUS USER RESPONSES IN THIS CONVERSATION:\n"
            context_str += "\n".join(previous_context[-2:])  # Only show last 2 for context

            if key_context_info:
                context_str += "\n\nKEY CONTEXT TO MAINTAIN:\n"
                context_str += "\n".join(key_context_info)

            context_str += "\n\nCRITICAL CONSISTENCY RULES:"
            context_str += "\n1. Your response must be logically consistent with the above context."
            context_str += "\n2. Do NOT contradict what the user has already established about account ownership."
            context_str += "\n3. If user said account belongs to someone else, do NOT say 'I tried resetting' - that's contradictory."
            context_str += "\n4. Stay consistent with the established narrative throughout the conversation.\n"
            return context_str

        return ""

    def generate_with_llm(self, step: Dict, context: Dict) -> str:
        """Generate user input using LLM"""
        if not self.llm:
            return self.generate_fallback(step)
        
        try:
            prompt = self.create_llm_prompt(step, context)
            response = self.llm.invoke([HumanMessage(content=prompt)])
            
            # Clean up response
            user_input = str(response.content).strip()
            user_input = user_input.replace('"', '').replace("'", "'")
            
            # Validate length
            if len(user_input.split()) > 20:
                print(f"  Warning: Generated input too long, using fallback")
                return self.generate_fallback(step)
            
            return user_input
            
        except Exception as e:
            print(f"  LLM generation failed: {e}, using fallback")
            return self.generate_fallback(step)
    
    def generate_fallback(self, step: Dict) -> str:
        """Generate fallback user input based on condition and node type"""
        condition = step.get('condition_trigger')
        node_type = step['node_type']
        node_title = step['node_title']

        # If we have a specific condition to trigger, create a simple response
        if condition:
            # For condition nodes, try to express the condition intent simply
            if 'yes' in condition.lower():
                return "Yes, that's correct."
            elif 'no' in condition.lower():
                return "No, that's not right."
            elif 'not the account owner' in condition.lower():
                return "This is not my account."
            elif 'reset record' in condition.lower():
                return "I tried resetting it before."
            elif 'automatic' in condition.lower() and 'refuse' in condition.lower():
                return "The system refused my request."
            else:
                # Generic response based on condition
                return f"I need help with {condition.lower()}."

        # Generic fallback based on node type
        if node_type == 'info_collection_node':
            return "I need help with my 2FA issue."
        elif node_type == 'condition_node':
            return "I'm not sure about that."
        else:
            return "I understand."
    
    def validate_input(self, user_input: str, step: Dict) -> Dict:
        """Validate generated user input with comprehensive scoring system"""
        validation = {
            'is_valid': True,
            'issues': [],
            'score': 0,  # Start from 0, build up based on criteria
            'comment': '',
            'information_adequacy': 'Unknown',
            'extraction_difficulty': 'Unknown',
            'scoring_breakdown': {}
        }

        node_type = step['node_type']
        condition = step.get('condition_trigger')
        requirements = step.get('input_requirements', {})

        # Comprehensive scoring system (0-10 scale)
        scoring = self.calculate_comprehensive_score(user_input, step)
        validation.update(scoring)

        # Assess information adequacy
        info_score = self.assess_information_adequacy(user_input, step)
        validation['information_adequacy'] = info_score['level']

        # Assess extraction difficulty
        difficulty_score = self.assess_extraction_difficulty(user_input, step)
        validation['extraction_difficulty'] = difficulty_score['level']

        # Build comment from scoring breakdown
        validation['comment'] = self.build_validation_comment(validation['scoring_breakdown'], info_score, difficulty_score)

        validation['is_valid'] = validation['score'] >= 4

        return validation

    def calculate_comprehensive_score(self, user_input: str, step: Dict) -> Dict:
        """Calculate comprehensive score with detailed breakdown"""
        breakdown = {}
        total_score = 0

        node_type = step['node_type']
        condition = step.get('condition_trigger')
        requirements = step.get('input_requirements', {})

        # 1. Essential Information (0-3 points)
        info_points = 0
        if condition:
            keywords = requirements.get('condition_keywords', [])
            if keywords:
                has_essential_info = any(keyword.lower() in user_input.lower() for keyword in keywords)
                if has_essential_info:
                    info_points = 3
                else:
                    info_points = 0
        else:
            # For non-condition nodes, check general appropriateness
            if node_type == 'Intent' and any(word in user_input.lower() for word in ['2fa', 'authenticator', 'phone', 'device', 'access']):
                info_points = 3
            elif node_type == 'Info Collection' and any(word in user_input.lower() for word in ['uid', 'id', 'account']):
                info_points = 3
            else:
                info_points = 2  # Partial credit for general relevance

        breakdown['essential_information'] = info_points
        total_score += info_points

        # 2. Natural Language Quality (0-2 points)
        natural_points = 0
        if any(pattern in user_input.lower() for pattern in ["can't", "don't", "i'm", "it's", "that's", "won't"]):
            natural_points += 1

        # Check for natural flow and realistic phrasing
        natural_phrases = ['you know', 'i mean', 'like', 'actually', 'really', 'just', 'basically']
        if any(phrase in user_input.lower() for phrase in natural_phrases):
            natural_points += 0.5

        # Penalize overly formal language
        formal_phrases = ['i would like to', 'please be advised', 'furthermore', 'in addition', 'consequently']
        if any(phrase in user_input.lower() for phrase in formal_phrases):
            natural_points -= 1

        breakdown['natural_language'] = max(0, min(2, natural_points))
        total_score += breakdown['natural_language']

        # 3. Realistic Context & Emotion (0-2 points)
        context_points = 0
        emotional_words = ['frustrated', 'urgent', 'asap', 'please', 'help', 'stuck', 'locked', 'broken', 'ridiculous']
        context_phrases = ['for days', 'since yesterday', 'right now', 'at work', 'this is', 'i need']

        if any(word in user_input.lower() for word in emotional_words):
            context_points += 1
        if any(phrase in user_input.lower() for phrase in context_phrases):
            context_points += 1

        breakdown['realistic_context'] = min(2, context_points)
        total_score += breakdown['realistic_context']

        # 4. Appropriate Length & Complexity (0-2 points)
        word_count = len(user_input.split())
        length_points = 0

        if 8 <= word_count <= 20:
            length_points = 2  # Optimal length
        elif 5 <= word_count <= 25:
            length_points = 1  # Acceptable length
        elif word_count < 5:
            length_points = 0  # Too short
        else:
            length_points = 0  # Too long

        breakdown['length_complexity'] = length_points
        total_score += length_points

        # 5. Extraction Challenge Level (0-1 points)
        challenge_points = 0
        # Reward inputs that have some complexity but aren't impossible
        filler_words = ['just', 'really', 'actually', 'basically', 'like', 'you know', 'i mean']
        filler_count = sum(1 for word in filler_words if word in user_input.lower())

        if 1 <= filler_count <= 3:  # Good balance of challenge
            challenge_points = 1
        elif filler_count > 3:  # Too much noise
            challenge_points = 0.5

        breakdown['extraction_challenge'] = challenge_points
        total_score += challenge_points

        return {
            'score': round(total_score, 1),
            'scoring_breakdown': breakdown
        }

    def build_validation_comment(self, breakdown: Dict, info_score: Dict, difficulty_score: Dict) -> str:
        """Build detailed validation comment from scoring breakdown"""
        comments = []

        # Essential information
        if breakdown['essential_information'] >= 3:
            comments.append("Contains all essential information")
        elif breakdown['essential_information'] >= 2:
            comments.append("Contains most required information")
        else:
            comments.append("Missing key information")

        # Natural language
        if breakdown['natural_language'] >= 1.5:
            comments.append("natural speech patterns")
        elif breakdown['natural_language'] >= 1:
            comments.append("somewhat natural expression")
        else:
            comments.append("formal/unnatural language")

        # Context and emotion
        if breakdown['realistic_context'] >= 1.5:
            comments.append("good emotional context")
        elif breakdown['realistic_context'] >= 1:
            comments.append("some contextual elements")

        # Length
        if breakdown['length_complexity'] >= 1.5:
            comments.append("appropriate length")
        else:
            comments.append("length issues")

        # Challenge level
        if breakdown['extraction_challenge'] >= 0.5:
            comments.append("appropriate extraction challenge")

        # Add difficulty assessment
        comments.append(f"{difficulty_score['level'].lower()} extraction difficulty")

        return "; ".join(comments).capitalize() + "."

    def assess_information_adequacy(self, user_input: str, step: Dict) -> Dict:
        """Assess if the input contains adequate information for the node"""
        node_type = step['node_type']
        condition = step.get('condition_trigger')

        if node_type == 'Intent':
            if any(word in user_input.lower() for word in ['2fa', 'authenticator', 'phone', 'device', 'access', 'login']):
                return {'level': 'Adequate', 'comment': 'Contains 2FA-related problem description.'}
            else:
                return {'level': 'Insufficient', 'comment': 'Missing clear 2FA problem indication.'}

        elif node_type == 'Info Collection':
            if any(word in user_input.lower() for word in ['uid', 'id', 'account', 'email', 'phone']):
                return {'level': 'Adequate', 'comment': 'Contains requested identification information.'}
            else:
                return {'level': 'Insufficient', 'comment': 'Missing requested information.'}

        elif node_type == 'Condition' and condition:
            keywords = step.get('input_requirements', {}).get('condition_keywords', [])
            if keywords and any(keyword.lower() in user_input.lower() for keyword in keywords):
                return {'level': 'Adequate', 'comment': 'Contains information for condition evaluation.'}
            else:
                return {'level': 'Insufficient', 'comment': 'Missing condition-specific information.'}

        return {'level': 'Adequate', 'comment': 'General response appropriate for node type.'}

    def assess_extraction_difficulty(self, user_input: str, step: Dict) -> Dict:
        """Assess the difficulty of extracting key information from the input"""
        word_count = len(user_input.split())

        # Count potential distractors
        filler_words = ['just', 'really', 'actually', 'basically', 'like', 'you know', 'i mean']
        filler_count = sum(1 for word in filler_words if word in user_input.lower())

        # Count emotional expressions
        emotional_expressions = ['please', 'asap', 'urgent', 'frustrated', 'stuck', 'help']
        emotion_count = sum(1 for expr in emotional_expressions if expr in user_input.lower())

        # Assess complexity
        if word_count <= 10 and filler_count == 0:
            return {'level': 'Easy', 'comment': 'Straightforward extraction.'}
        elif word_count <= 15 and filler_count <= 2:
            return {'level': 'Moderate', 'comment': 'Some context but clear key information.'}
        elif word_count <= 25 or filler_count > 2 or emotion_count > 1:
            return {'level': 'Challenging', 'comment': 'Requires parsing through context and emotion.'}
        else:
            return {'level': 'Difficult', 'comment': 'Complex extraction with multiple distractors.'}

    
    def generate_inputs_for_path(self, path_info: Dict) -> Dict:
        """Generate user inputs for entire path"""
        print(f"Generating natural inputs for {path_info['path_id']}")
        print("=" * 60)
        
        generated_sequence = []
        context = {
            'path_id': path_info['path_id'],
            'total_steps': path_info['total_steps'],
            'path_data': path_info  # Pass complete path data for context
        }
        
        for step in path_info['steps']:
            # Skip Start and Intent nodes - they don't need user input
            if step['node_type'] in ['start_node', 'intent_node']:
                # Don't print or include skipped nodes in output
                continue

            print(f"Step {step['step']}: {step['node_type']} - {step['node_title']}")

            # Skip other steps that don't need user input
            if not step.get('input_requirements', {}).get('needs_user_input', True):
                print("  → No user input needed")
                generated_sequence.append({
                    'step': step['step'],
                    'node_type': step['node_type'],
                    'node_title': step['node_title'],
                    'user_input': None,
                    'condition_trigger': step.get('condition_trigger')
                })
                continue
            
            # Generate user input
            if self.llm:
                user_input = self.generate_with_llm(step, context)
                print(f"  → LLM: \"{user_input}\"")
            else:
                user_input = self.generate_fallback(step)
                print(f"  → Fallback: \"{user_input}\"")
            
            # Validate input
            validation = self.validate_input(user_input, step)
            print(f"  → Validation: {validation['score']}/10", end="")
            if validation['issues']:
                print(f" (Issues: {', '.join(validation['issues'])})")
            else:
                print(" ✓")
            
            generated_sequence.append({
                'step': step['step'],
                'node_type': step['node_type'],
                'node_title': step['node_title'],
                'user_input': user_input,
                'condition_trigger': step.get('condition_trigger'),
                'validation': validation
            })
            
            print()
        
        # Create final output
        output = {
            'path_id': path_info['path_id'],
            'total_steps': len(generated_sequence),
            'description': f"Natural user inputs for {path_info['path_id']}",
            'language': 'English',
            'style': 'Natural and concise',
            'generation_summary': {
                'total_inputs': len([s for s in generated_sequence if s['user_input']]),
                'avg_validation_score': 0,
                'issues_found': 0
            },
            'sequence': generated_sequence
        }
        
        # Calculate summary stats
        valid_inputs = [s for s in generated_sequence if s['user_input'] and 'validation' in s]
        if valid_inputs:
            scores = [s['validation']['score'] for s in valid_inputs]
            output['generation_summary']['avg_validation_score'] = sum(scores) / len(scores)
            output['generation_summary']['issues_found'] = sum(1 for s in valid_inputs if s['validation']['issues'])
        
        return output

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python generate_natural_inputs.py <path_info_file>")
        print("Example: python generate_natural_inputs.py path_0013_extracted_info.json")
        return
    
    path_info_file = sys.argv[1]
    
    try:
        generator = NaturalInputGenerator()
        path_info = generator.load_path_info(path_info_file)
        
        # Generate inputs
        result = generator.generate_inputs_for_path(path_info)
        
        # Save result
        output_file = f"{path_info['path_id']}_natural_inputs.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"✓ Natural inputs generated and saved to {output_file}")
        print(f"Summary:")
        print(f"  - Total inputs: {result['generation_summary']['total_inputs']}")
        print(f"  - Avg validation score: {result['generation_summary']['avg_validation_score']:.1f}/10")
        print(f"  - Issues found: {result['generation_summary']['issues_found']}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
