{"path_id": "path_0001", "description": "Start-->Intent:2FA cannot be used-->Info Collection:User provide or confirm UID to reset 2FA-->Condition:[Ask User] Is the User request to reset on the account user logged in or associated to?-(User is account owner and associated)->Condition:Which 2FA User wants to reset? Has reset or not?-(has reset record)->Condition:User self reset result (updated)-(Automatic review passed / Manual review passed)->Info Expression:05. User's self-reset is approved and can login with the new 2FAs", "total_steps": 5, "steps": [{"step": 3, "node_id": "info_collection_node_864673c7-e8c1-4658-a627-bb63eff1d304", "node_type": "info_collection_node", "node_title": "User provide or confirm UID to reset 2FA", "node_description": "", "guidance": null, "conditions": [], "collection_info": [], "needs_user_input": true, "input_requirements": {"input_type": "information_collection", "description": "", "guidance": null, "collection_info": []}}, {"step": 4, "node_id": "condition_node__17325ccc-4b31-4292-804b-52271b501d70", "node_type": "condition_node", "node_title": "[Ask User] Is the User request to reset on the account user logged in or associated to?", "node_description": "", "guidance": null, "conditions": [{"conditionName": "", "logicOperator": "User is not the account owner", "handle": "condition_node__17325ccc-4b31-4292-804b-52271b501d70__21193a97-05da-4670-88d5-6ee67f37ac9b"}, {"logicOperator": "User is account owner and associated", "handle": "condition_node__17325ccc-4b31-4292-804b-52271b501d70__d0712d00-97cb-4c32-829f-05d48db9a7db"}, {"logicOperator": "User is account owner and not associated", "handle": "condition_node__17325ccc-4b31-4292-804b-52271b501d70__767781cb-1f29-4e6c-938b-c545d13764b4"}], "collection_info": [], "needs_user_input": true, "input_requirements": {"input_type": "decision_trigger", "description": "", "guidance": null, "collection_info": [], "available_conditions": ["User is not the account owner", "User is account owner and associated", "User is account owner and not associated"], "valid_conditions": ["User is not the account owner", "User is account owner and associated", "User is account owner and not associated"]}, "condition_trigger": "User is account owner and associated"}, {"step": 5, "node_id": "condition_node_6e912c94-7824-429e-97c9-8d56bcd46da6", "node_type": "condition_node", "node_title": "Which 2FA User wants to reset? Has reset or not?", "node_description": "", "guidance": null, "conditions": [{"conditionName": "", "rules": [{"logicOperator": "and", "variable": "4210ddbc-eb81-4379-8a74-4ad8456b662c", "value": "YES", "operator": "="}], "handle": "condition_node_6e912c94-7824-429e-97c9-8d56bcd46da6_e4236a0c-a720-4398-8644-12d91a125ae3", "logicOperator": "has reset record"}, {"rules": [{"logicOperator": "and", "variable": "08d66a45-9f16-4475-8689-0446e810fbef", "value": "User choice", "operator": "="}], "logicOperator": "(Didn't reset) User to advise which 2FA to be reset", "handle": "condition_node_6e912c94-7824-429e-97c9-8d56bcd46da6_e43efcb5-557a-4f19-809d-0750d055828a"}, {"rules": [{"logicOperator": "and", "variable": "d40cf181-7a89-481a-8893-940fc6ca2162", "value": "YES", "operator": "="}], "logicOperator": "has reset 2FA appeal", "handle": "condition_node_6e912c94-7824-429e-97c9-8d56bcd46da6_df9f880a-48c5-47bf-9739-598cf0601f23"}], "collection_info": [], "needs_user_input": true, "input_requirements": {"input_type": "decision_trigger", "description": "", "guidance": null, "collection_info": [], "available_conditions": ["has reset record", "(Didn't reset) User to advise which 2FA to be reset", "has reset 2FA appeal"], "valid_conditions": ["has reset record", "(Didn't reset) User to advise which 2FA to be reset", "has reset 2FA appeal"]}, "condition_trigger": "has reset record"}, {"step": 6, "node_id": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a", "node_type": "condition_node", "node_title": "User self reset result (updated)", "node_description": "", "guidance": null, "conditions": [{"conditionName": "", "rules": [{"logicOperator": "and", "variable": "4210ddbc-eb81-4379-8a74-4ad8456b662c", "operator": "=", "value": "Pending Review"}], "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_52a2725f-9588-49fb-a392-838c3a032668", "logicOperator": "Pending Review", "description": ""}, {"rules": [{"logicOperator": "and", "variable": "4210ddbc-eb81-4379-8a74-4ad8456b662c", "operator": "=", "value": "Automatic review passed / Manual review passed"}], "logicOperator": "Automatic review passed / Manual review passed", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_2ba3badb-8439-425f-80d3-65a582bc5b37", "description": ""}, {"rules": [{"logicOperator": "and", "variable": "4210ddbc-eb81-4379-8a74-4ad8456b662c", "operator": "=", "value": "Automatic review refuse"}], "logicOperator": "Automatic review refuse", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_1860a309-7eda-45f7-8e1f-d909db7c9661", "description": "reject reason = AI"}, {"rules": [{"logicOperator": "and", "variable": "4210ddbc-eb81-4379-8a74-4ad8456b662c", "value": "MFA_REJECTION_FACE _ATTACK_AI", "operator": "="}], "logicOperator": "Manual review refuse - MFA_REJECTION_FACE _ATTACK_AI", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_8a5c8407-cacc-42d8-98cd-8d23b2e4fce1", "description": "MFA_REJECTION_DEFAULT_CONTACT_CS -> redirect to \"Asset check\""}, {"rules": [{"logicOperator": "and", "variable": "ebeae2d8-11e6-48f3-958f-acdc026be656", "value": "MFA_REJECTION_FACE _ATTACK_NON_AI", "operator": "="}], "logicOperator": "Manual review refuse - MFA_REJECTION_FACE _ATTACK_NON_AI", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_0f630478-4c54-4573-9668-3920bbd941b9"}, {"rules": [{"logicOperator": "and", "variable": "ebeae2d8-11e6-48f3-958f-acdc026be656", "value": "MFA_REJECTION_SELFIE_MULTIPLE_FACE", "operator": "="}], "logicOperator": "Manual review refuse - MFA_REJECTION_SELFIE_MULTIPLE_FACE", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_2737551c-f214-4f65-85fd-916143b0e3fb"}, {"rules": [{"logicOperator": "and", "variable": "ebeae2d8-11e6-48f3-958f-acdc026be656", "value": "MFA_REJECTION_MULTIPLE_PEOPLE", "operator": "="}], "logicOperator": "Manual review refuse - MFA_REJECTION_MULTIPLE_PEOPLE", "description": "Redirect to \"Asset check\"", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_bc27152f-ab6e-440a-8404-b95d570dee1d"}, {"rules": [{"logicOperator": "and", "variable": "ebeae2d8-11e6-48f3-958f-acdc026be656", "value": "MFA_REJECTION_SPECIAL_REMARK", "operator": "="}], "logicOperator": "Manual review refuse - MFA_REJECTION_SPECIAL_REMARK", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_2ba90d10-3761-406c-80d3-53c8fc7fc842"}, {"rules": [{"logicOperator": "and", "variable": "ebeae2d8-11e6-48f3-958f-acdc026be656", "value": "MFA_REJECTION_OFFBOARD", "operator": "="}], "logicOperator": "Manual review refuse - MFA_REJECTION_OFFBOARD", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_ee6f057f-acd2-4ee1-9534-52b1f38615c1"}, {"rules": [{"logicOperator": "and", "variable": "ebeae2d8-11e6-48f3-958f-acdc026be656", "value": "MFA_REJECTION_DOCUMENT_REQUEST / MFA_REJECTION_LOW_ LIVENESS / MFA_REJECTION_DEVICE_IP_ISSUE / MFA_REJECTION_DEFAULT_CONTACT_CS", "operator": "="}], "logicOperator": "Manual review refuse - MFA_REJECTION_DOCUMENT_REQUEST / MFA_REJECTION_LOW_ LIVENESS / MFA_REJECTION_DEVICE_IP_ISSUE / MFA_REJECTION_DEFAULT_CONTACT_CS", "handle": "condition_node_3647d8fb-e991-4773-9947-1361ee4d5d1a_55aea228-434a-4a97-b981-63734375f83c"}], "collection_info": [], "needs_user_input": true, "input_requirements": {"input_type": "decision_trigger", "description": "", "guidance": null, "collection_info": [], "available_conditions": ["Pending Review", "Automatic review passed / Manual review passed", "Automatic review refuse", "Manual review refuse - MFA_REJECTION_FACE _ATTACK_AI", "Manual review refuse - MFA_REJECTION_FACE _ATTACK_NON_AI", "Manual review refuse - MFA_REJECTION_SELFIE_MULTIPLE_FACE", "Manual review refuse - MFA_REJECTION_MULTIPLE_PEOPLE", "Manual review refuse - MFA_REJECTION_SPECIAL_REMARK", "Manual review refuse - MFA_REJECTION_OFFBOARD", "Manual review refuse - MFA_REJECTION_DOCUMENT_REQUEST / MFA_REJECTION_LOW_ LIVENESS / MFA_REJECTION_DEVICE_IP_ISSUE / MFA_REJECTION_DEFAULT_CONTACT_CS"], "valid_conditions": ["Pending Review", "Automatic review passed / Manual review passed", "Automatic review refuse", "Manual review refuse - MFA_REJECTION_FACE _ATTACK_AI", "Manual review refuse - MFA_REJECTION_FACE _ATTACK_NON_AI", "Manual review refuse - MFA_REJECTION_SELFIE_MULTIPLE_FACE", "Manual review refuse - MFA_REJECTION_MULTIPLE_PEOPLE", "Manual review refuse - MFA_REJECTION_SPECIAL_REMARK", "Manual review refuse - MFA_REJECTION_OFFBOARD", "Manual review refuse - MFA_REJECTION_DOCUMENT_REQUEST / MFA_REJECTION_LOW_ LIVENESS / MFA_REJECTION_DEVICE_IP_ISSUE / MFA_REJECTION_DEFAULT_CONTACT_CS"]}, "condition_trigger": "Automatic review passed / Manual review passed"}, {"step": 7, "node_id": "info_expression_node_a78a4509-0f29-4bc2-8178-70fabbd15ecf", "node_type": "info_expression_node", "node_title": "05. User's self-reset is approved and can login with the new 2FAs", "node_description": "", "guidance": null, "conditions": [], "collection_info": [], "needs_user_input": false}]}