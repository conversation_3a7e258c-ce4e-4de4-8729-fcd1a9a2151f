# Path-to-Inputs Workflow Guide

This workflow extracts SOP path information and generates realistic user inputs for testing your SOP system's robustness.

## 🎯 Purpose

Generate **realistic, challenging user inputs** that:
- Contain essential information for path routing
- Include natural context, emotions, and minor distractions
- Test your SOP system's NLP extraction robustness
- Simulate real customer conversations

## 🔧 Components

1. **`extract_path_info.py`** - Extracts structured info from any path
2. **`generate_natural_inputs.py`** - Generates realistic user inputs using LLM
3. **`path_to_inputs_workflow.py`** - Main workflow orchestrator

## 🚀 Basic Usage

### Process a single path
```bash
python path_to_inputs_workflow.py --path_id path_0013
```

### Process multiple paths
```bash
python path_to_inputs_workflow.py --path_ids path_0013 path_0001 path_0005
```

### List all available paths
```bash
python path_to_inputs_workflow.py --list_paths
```

### Find long paths (15+ nodes)
```bash
python path_to_inputs_workflow.py --find_long 15
```

### Specify output directory
```bash
python path_to_inputs_workflow.py --path_id path_0013 --output_dir ./results
```

## 🔄 Individual Components

### Extract path info only
```bash
python extract_path_info.py path_0013
# → Generates: path_0013_extracted_info.json
```

### Generate inputs from extracted info
```bash
python generate_natural_inputs.py path_0013_extracted_info.json
# → Generates: path_0013_natural_inputs.json
```

## 📁 Output Files

For each processed path, you get:

### 1. `{path_id}_extracted_info.json`
- Structured path information
- Node types, titles, conditions
- SOP data matches
- Input requirements

### 2. `{path_id}_natural_inputs.json`
- Realistic user input sequence
- Validation scores and comments
- Information adequacy assessment
- Extraction difficulty rating

### 3. `{path_id}_summary_report.json`
- Quality assessment
- Processing summary
- Recommendations

## 🎭 Input Generation Philosophy

### Node Handling
- **Start nodes**: Automatically skipped - no user input needed
- **Intent nodes**: Automatically skipped - no user input needed
- **Info Collection nodes**: Generate user inputs providing requested information
- **Condition nodes**: Generate inputs that trigger specific path conditions
- **Info Expression nodes**: Generate acknowledgment responses

### Realistic User Behavior
- **Emotional context**: Frustrated but cooperative
- **Natural speech**: Contractions, redundancy, tangents
- **Information embedding**: Key info present but naturally expressed
- **Appropriate difficulty**: Challenging but fair for NLP extraction

### Examples of Generated Inputs

**Too Clean (Avoided)**:
```
"I have less than 1 BTC."
```

**Realistic (Generated)**:
```
"My account doesn't have much, just under 1 BTC I think."
```

**Too Clean (Avoided)**:
```
"I cannot provide the video."
```

**Realistic (Generated)**:
```
"I can't do that video thing right now, sorry."
```

## 📊 Validation System

### Information Adequacy
- **Adequate**: Contains essential information for routing
- **Insufficient**: Missing key information

### Extraction Difficulty
- **Easy**: Straightforward, minimal context
- **Moderate**: Some context but clear key information
- **Challenging**: Requires parsing through context and emotion
- **Difficult**: Complex extraction with multiple distractors

### Quality Comments
Each generated input includes detailed comments about:
- Whether it follows node requirements
- Information sufficiency for routing decisions
- Extraction difficulty level
- Presence of natural speech patterns

## 🧪 Testing Integration

### Load Generated Test Data
```python
import json

# Load generated inputs
with open('path_0013_natural_inputs.json', 'r') as f:
    data = json.load(f)

# Extract user inputs for testing
user_inputs = []
for step in data['sequence']:
    if step['user_input']:
        user_inputs.append({
            'input': step['user_input'],
            'expected_condition': step.get('condition_trigger'),
            'difficulty': step['validation']['extraction_difficulty'],
            'comment': step['validation']['comment']
        })
```

### Test SOP Robustness
```python
# Test with your SOP system
for test_case in user_inputs:
    user_input = test_case['input']
    expected = test_case['expected_condition']
    difficulty = test_case['difficulty']
    
    # Process with your SOP system
    result = your_sop_system.process(user_input)
    
    print(f"Input: {user_input}")
    print(f"Expected: {expected}")
    print(f"Difficulty: {difficulty}")
    print(f"Result: {result}")
    print(f"Success: {check_routing_success(result, expected)}")
    print("-" * 50)
```

## 🎯 Quality Assessment

### Scoring Criteria
- **8-10**: Excellent - Realistic with appropriate challenge
- **6-7**: Good - Minor improvements needed
- **4-5**: Fair - Needs revision
- **0-3**: Poor - Regenerate

### Key Factors
1. **Information Presence**: Essential data for routing
2. **Natural Expression**: Realistic speech patterns
3. **Appropriate Challenge**: Not too easy, not impossible
4. **Emotional Context**: Frustrated customer tone
5. **Extraction Difficulty**: Tests NLP robustness

## 🔧 Customization

### Modify LLM Prompts
Edit `generate_natural_inputs.py`:
- Adjust `create_llm_prompt()` for different user personas
- Modify difficulty levels in prompt requirements
- Add domain-specific context

### Adjust Validation Criteria
Edit validation functions:
- `assess_information_adequacy()` - Information requirements
- `assess_extraction_difficulty()` - Difficulty assessment
- `validate_input()` - Overall scoring

### Add New Condition Templates
Edit fallback templates in `generate_fallback()` for new condition types.

## 🚨 Troubleshooting

### "Path not found" error
- Check path_id spelling
- Use `--list_paths` to see available paths

### "LLM initialization failed"
- Check sop_core.py LLM configuration
- Workflow will use fallback templates

### "Missing SOP data files"
- Ensure `sop_langgraph_data.json` exists
- Ensure `sop_paths_node_only_descriptions.json` exists

### Low quality scores
- Review generated inputs manually
- Check if essential information is present but naturally embedded
- Verify condition trigger accuracy

## 💡 Best Practices

1. **Start with diverse paths** to test different scenarios
2. **Review validation comments** to understand generation quality
3. **Test extraction difficulty** matches your system's capabilities
4. **Use batch processing** for comprehensive testing
5. **Validate routing accuracy** with generated inputs
6. **Keep generated files** for regression testing

## 📈 Workflow Examples

### Example 1: Quick Testing Setup
```bash
# Find interesting paths
python path_to_inputs_workflow.py --list_paths

# Process a complex path
python path_to_inputs_workflow.py --path_id path_0013

# Review quality
cat path_0013_natural_inputs.json | jq '.generation_summary'
```

### Example 2: Comprehensive Testing
```bash
# Find all long paths
python path_to_inputs_workflow.py --find_long 10

# Process multiple paths
python path_to_inputs_workflow.py --path_ids path_0013 path_0001 path_0005 --output_dir test_suite

# Review batch results
cat test_suite/batch_processing_results.json
```

### Example 3: Quality Improvement
```bash
# Process path
python path_to_inputs_workflow.py --path_id path_0013

# Check validation details
cat path_0013_natural_inputs.json | jq '.sequence[].validation'

# Regenerate if needed with modified prompts
```

## 🎪 Advanced Usage

### Custom Output Directory Structure
```bash
mkdir -p test_cases/{easy,moderate,challenging}
python path_to_inputs_workflow.py --path_id path_0013 --output_dir test_cases/challenging
```

### Filtering by Difficulty
```python
# Load and filter by extraction difficulty
with open('path_0013_natural_inputs.json', 'r') as f:
    data = json.load(f)

challenging_cases = [
    step for step in data['sequence'] 
    if step.get('validation', {}).get('extraction_difficulty') == 'Challenging'
]
```

---

## 📞 Support

For issues or questions:
1. Check generated validation comments for insights
2. Review extraction difficulty ratings
3. Examine information adequacy assessments
4. Verify essential information presence in inputs
