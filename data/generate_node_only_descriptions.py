#!/usr/bin/env python3
"""
生成node_only路径的简化描述文件
只包含SOP_path_id和SOP_path_description两个字段
"""

import json
import os
from typing import List, Dict, Any

def extract_node_only_descriptions(input_file: str, output_file: str) -> None:
    """
    从node_only路径文件中提取简化的描述信息
    
    Args:
        input_file: 输入的node_only路径文件
        output_file: 输出的简化描述文件
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"输入文件不存在: {input_file}")
    
    print(f"正在读取输入文件: {input_file}")
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 提取路径数据
    paths = data.get('paths', [])
    
    # 创建简化的路径描述列表
    simplified_paths = []
    
    for path in paths:
        simplified_path = {
            "SOP_path_id": path.get('path_id', ''),
            "SOP_path_description": path.get('description', '')
        }
        simplified_paths.append(simplified_path)
    
    print(f"已处理 {len(simplified_paths)} 条路径")
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(simplified_paths, f, ensure_ascii=False, indent=2)
    
    print(f"简化描述文件已生成: {output_file}")
    print(f"总共包含 {len(simplified_paths)} 条路径描述")


def main():
    """主函数"""
    input_file = "sop_paths_extracted_nodes_only.json"
    output_file = "sop_paths_node_only_descriptions.json"
    
    try:
        extract_node_only_descriptions(input_file, output_file)
        
        # 显示前几条示例
        print("\n前3条路径描述示例:")
        with open(output_file, 'r', encoding='utf-8') as f:
            simplified_data = json.load(f)
            
        for i, path in enumerate(simplified_data[:3]):
            print(f"\n路径 {i+1}:")
            print(f"  SOP_path_id: {path['SOP_path_id']}")
            print(f"  SOP_path_description: {path['SOP_path_description'][:100]}...")
            
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main() 