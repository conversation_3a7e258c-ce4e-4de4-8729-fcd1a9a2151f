#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOP JSON 转换脚本 
将真实的SOP JSON数据转换为LangGraph格式，过滤冗余边连接
"""

import json
import os
from typing import Dict, List, Any
from collections import defaultdict

def load_sop_json(file_path: str) -> Dict[str, Any]:
    """加载SOP JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 成功加载 {file_path}")
        return data
    except Exception as e:
        print(f"❌ 加载文件失败: {e}")
        return {}

def extract_nodes_info(nodes: List[Dict]) -> Dict[str, Dict]:
    """提取节点信息并构建节点映射"""
    
    node_mapping = {}
    node_types = defaultdict(int)
    
    print(f"📊 开始解析 {len(nodes)} 个节点...")
    
    for node in nodes:
        node_id = node.get('id')
        node_data = node.get('data', {})
        node_type = node_data.get('type', 'unknown')
        
        node_types[node_type] += 1
        
        # 提取节点信息
        node_info = {
            'id': node_id,
            'type': node_type,
            'position': node.get('position', {}),
            'data': node_data.get('value', {}),
            'raw_data': node_data
        }
        
        # 根据节点类型提取具体信息
        if node_type == 'start_node':
            node_info['is_start'] = True
            
        elif node_type == 'intent_node':
            value = node_data.get('value', {})
            node_info['title'] = value.get('title', '')
            node_info['intent_code'] = value.get('intentCode', '')
            node_info['description'] = value.get('description', '')
            
        elif node_type == 'info_collection_node':
            value = node_data.get('value', {})
            node_info['title'] = value.get('title', '')
            node_info['description'] = value.get('description', '')
            node_info['user_type'] = value.get('userType', '')
            node_info['collection_info'] = value.get('collectionInfoList', [])
            
        elif node_type == 'info_expression_node':
            value = node_data.get('value', {})
            node_info['title'] = value.get('title', '')
            node_info['description'] = value.get('description', '')
            node_info['expression_type'] = value.get('expressionType', '')
            node_info['content'] = value.get('content', '')
            
        elif node_type == 'condition_node':
            value = node_data.get('value', {})
            node_info['title'] = value.get('title', '')
            node_info['conditions'] = value.get('conditions', [])
            node_info['is_llm_decision'] = True  # condition_node通常需要LLM决策
            
        elif node_type == 'end_node':
            node_info['is_end'] = True
            
        node_mapping[node_id] = node_info
    
    print(f"📈 节点类型统计:")
    for node_type, count in node_types.items():
        print(f"   {node_type}: {count}")
    
    return node_mapping

def filter_valid_edges(edges: List[Dict], node_mapping: Dict[str, Dict]) -> List[Dict]:
    """过滤有效边，去除指向不存在节点的边"""
    
    valid_edges = []
    invalid_edges = []
    node_ids = set(node_mapping.keys())
    
    print(f"📊 开始过滤 {len(edges)} 条边...")
    print(f"🔍 节点总数: {len(node_ids)}")
    
    for edge in edges:
        source = edge.get('source')
        target = edge.get('target')
        
        # 检查源节点和目标节点是否存在
        source_exists = source in node_ids
        target_exists = target in node_ids
        
        if source_exists and target_exists:
            # 提取边的条件信息
            edge_info = {
                'source': source,
                'target': target,
                'source_handle': edge.get('sourceHandle', ''),
                'target_handle': edge.get('targetHandle', ''),
                'edge_id': edge.get('id', ''),
                'condition': None  # 从sourceHandle提取条件
            }
            
            # 从sourceHandle提取条件标识
            source_handle = edge.get('sourceHandle', '')
            if '_' in source_handle and source_handle != source:
                # 提取条件ID (通常在最后一个下划线后)
                parts = source_handle.split('_')
                if len(parts) > 1:
                    edge_info['condition'] = parts[-1]
            
            valid_edges.append(edge_info)
        else:
            invalid_info = {
                'source': source,
                'target': target,
                'source_exists': source_exists,
                'target_exists': target_exists
            }
            invalid_edges.append(invalid_info)
    
    print(f"✅ 有效边: {len(valid_edges)}")
    print(f"❌ 无效边: {len(invalid_edges)}")
    
    if invalid_edges:
        print(f"🔍 无效边详情:")
        for i, invalid in enumerate(invalid_edges[:10]):  # 只显示前10个
            print(f"   {i+1}. {invalid['source']} -> {invalid['target']}")
            print(f"      源存在: {invalid['source_exists']}, 目标存在: {invalid['target_exists']}")
        if len(invalid_edges) > 10:
            print(f"   ... 还有 {len(invalid_edges) - 10} 个无效边")
    
    return valid_edges

def build_adjacency_graph(edges: List[Dict]) -> Dict[str, List[Dict]]:
    """构建邻接图，用于路径规划"""
    
    graph = defaultdict(list)
    
    for edge in edges:
        source = edge['source']
        target = edge['target']
        condition = edge.get('condition')
        
        graph[source].append({
            'target': target,
            'condition': condition,
            'edge_info': edge
        })
    
    return dict(graph)

def find_start_nodes(node_mapping: Dict[str, Dict]) -> List[str]:
    """找到起始节点"""
    start_nodes = []
    for node_id, node_info in node_mapping.items():
        if node_info.get('is_start', False) or node_info['type'] == 'start_node':
            start_nodes.append(node_id)
    
    print(f"🏁 找到 {len(start_nodes)} 个起始节点: {start_nodes}")
    return start_nodes

def convert_to_langgraph_format(node_mapping: Dict[str, Dict], adjacency_graph: Dict[str, List[Dict]]) -> Dict[str, Any]:
    """转换为LangGraph格式"""
    
    # 生成LangGraph节点配置
    langgraph_nodes = {}
    
    for node_id, node_info in node_mapping.items():
        node_type = node_info['type']
        
        # 根据节点类型映射到LangGraph节点类型
        if node_type == 'start_node':
            lg_type = 'InfoExpression'
        elif node_type == 'intent_node':
            lg_type = 'Intent'  # intent_node单独类型
        elif node_type == 'info_collection_node':
            lg_type = 'InfoCollection'
        elif node_type == 'info_expression_node':
            lg_type = 'InfoExpression'
        elif node_type == 'condition_node':
            lg_type = 'Conditional_LLM'
        elif node_type == 'end_node':
            lg_type = 'InfoExpression'
        else:
            lg_type = 'InfoExpression'  # 默认类型
        
        langgraph_nodes[node_id] = {
            'id': node_id,
            'type': lg_type,
            'title': node_info.get('title', ''),
            'description': node_info.get('description', ''),
            'original_type': node_type,
            'next_nodes': [edge['target'] for edge in adjacency_graph.get(node_id, [])],
            'conditions': node_info.get('conditions', []),
            'collection_info': node_info.get('collection_info', []),
            'content': node_info.get('content', ''),
            'raw_data': node_info.get('raw_data', {})
        }
    
    # 找到起始节点
    start_nodes = find_start_nodes(node_mapping)
    
    result = {
        'nodes': langgraph_nodes,
        'adjacency_graph': adjacency_graph,
        'start_nodes': start_nodes,
        'total_nodes': len(langgraph_nodes),
        'total_edges': sum(len(edges) for edges in adjacency_graph.values())
    }
    
    return result

def save_converted_data(converted_data: Dict[str, Any], output_file: str):
    """保存转换后的数据"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, indent=2, ensure_ascii=False)
        print(f"✅ 转换结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存失败: {e}")

def convert_sop_json_to_langgraph(input_file: str, output_file: str = None) -> Dict[str, Any]:
    """主转换函数"""
    
    print("🚀 开始SOP JSON转换...")
    print("="*60)
    
    # 1. 加载JSON数据
    sop_data = load_sop_json(input_file)
    if not sop_data:
        return {}
    
    nodes = sop_data.get('nodes', [])
    edges = sop_data.get('edges', [])
    
    print(f"📊 原始数据统计:")
    print(f"   节点数: {len(nodes)}")
    print(f"   边数: {len(edges)}")
    
    # 2. 提取节点信息
    node_mapping = extract_nodes_info(nodes)
    
    # 3. 过滤有效边
    valid_edges = filter_valid_edges(edges, node_mapping)
    
    # 4. 构建邻接图
    adjacency_graph = build_adjacency_graph(valid_edges)
    
    # 5. 转换为LangGraph格式
    converted_data = convert_to_langgraph_format(node_mapping, adjacency_graph)
    
    print(f"")
    print(f"📊 转换结果统计:")
    print(f"   LangGraph节点数: {converted_data['total_nodes']}")
    print(f"   LangGraph边数: {converted_data['total_edges']}")
    print(f"   起始节点: {converted_data['start_nodes']}")
    
    # 6. 保存结果
    if output_file:
        save_converted_data(converted_data, output_file)
    
    print("✅ SOP JSON转换完成!")
    print("="*60)
    
    return converted_data

if __name__ == "__main__":
    # 转换真实SOP数据
    input_file = "reset_2fa_final.json"
    output_file = "sop_langgraph_data.json"
    
    if os.path.exists(input_file):
        converted_data = convert_sop_json_to_langgraph(input_file, output_file)
        
        # 显示一些示例节点
        if converted_data and 'nodes' in converted_data:
            print(f"\n🔍 示例节点预览:")
            node_count = 0
            for node_id, node_info in converted_data['nodes'].items():
                print(f"   {node_id[:30]}... ({node_info['type']}) - {node_info['title'][:50]}...")
                node_count += 1
                if node_count >= 5:
                    break
    else:
        print(f"❌ 文件不存在: {input_file}") 