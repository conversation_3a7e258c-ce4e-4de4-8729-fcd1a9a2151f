{"path_id": "path_0000", "description": "Start-->Intent:2FA cannot be used-->Info Collection:User provide or confirm UID to reset 2FA-->Condition:[Ask User] Is the User request to reset on the account user logged in or associated to?-(User is not the account owner)->Info Expression:01. Request User to chat in again as Visitor or login to the account User want to perform 2FA reset", "total_steps": 3, "steps": [{"step": 3, "node_id": "info_collection_node_864673c7-e8c1-4658-a627-bb63eff1d304", "node_type": "info_collection_node", "node_title": "User provide or confirm UID to reset 2FA", "node_description": "", "guidance": null, "conditions": [], "collection_info": [], "needs_user_input": true, "input_requirements": {"input_type": "information_collection", "description": "", "guidance": null, "collection_info": []}}, {"step": 4, "node_id": "condition_node__17325ccc-4b31-4292-804b-52271b501d70", "node_type": "condition_node", "node_title": "[Ask User] Is the User request to reset on the account user logged in or associated to?", "node_description": "", "guidance": null, "conditions": [{"conditionName": "", "logicOperator": "User is not the account owner", "handle": "condition_node__17325ccc-4b31-4292-804b-52271b501d70__21193a97-05da-4670-88d5-6ee67f37ac9b"}, {"logicOperator": "User is account owner and associated", "handle": "condition_node__17325ccc-4b31-4292-804b-52271b501d70__d0712d00-97cb-4c32-829f-05d48db9a7db"}, {"logicOperator": "User is account owner and not associated", "handle": "condition_node__17325ccc-4b31-4292-804b-52271b501d70__767781cb-1f29-4e6c-938b-c545d13764b4"}], "collection_info": [], "needs_user_input": true, "input_requirements": {"input_type": "decision_trigger", "description": "", "guidance": null, "collection_info": [], "available_conditions": ["User is not the account owner", "User is account owner and associated", "User is account owner and not associated"]}, "condition_trigger": "User is not the account owner"}, {"step": 5, "node_id": "info_expression_node_f6f48e32-9ca8-4a70-bbc6-fd400ef843c1", "node_type": "info_expression_node", "node_title": "01. Request User to chat in again as Visitor or login to the account User want to perform 2FA reset", "node_description": "", "guidance": null, "conditions": [], "collection_info": [], "needs_user_input": false}]}