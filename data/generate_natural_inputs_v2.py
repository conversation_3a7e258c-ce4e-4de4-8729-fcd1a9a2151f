#!/usr/bin/env python3
"""
基于提取的路径信息生成自然、简洁的用户输入
重构版本：专注于正确率和上下文一致性
"""

import json
import sys
import os
from typing import Dict, List, Optional
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from sop_core import create_llm
from langchain_core.messages import HumanMessage

class NaturalInputGenerator:
    def __init__(self):
        self.llm = None
        self.conversation_history = []  # 跟踪对话历史
        self.init_llm()
    
    def init_llm(self):
        """Initialize LLM"""
        try:
            self.llm = create_llm()
            print("✓ LLM initialized successfully")
        except Exception as e:
            print(f"✗ Failed to initialize LLM: {e}")
            self.llm = None
    
    def create_prompt(self, step: Dict, context: Dict) -> str:
        """创建LLM prompt，基于节点信息和上下文"""
        node_type = step['node_type']
        node_title = step['node_title']
        condition_trigger = step.get('condition_trigger')
        current_step = step['step']
        
        # 构建基础prompt
        prompt = f"""You are generating a realistic user response for a 2FA support conversation.

CURRENT SITUATION:
- Step {current_step}: {node_type}
- Agent question: "{node_title}"
"""
        
        # 添加对话历史上下文
        if self.conversation_history:
            prompt += "\nPREVIOUS USER RESPONSES:\n"
            for i, prev_response in enumerate(self.conversation_history[-3:], 1):  # 只显示最近3个
                prompt += f"Step {prev_response['step']}: \"{prev_response['user_input']}\"\n"
            prompt += "\nIMPORTANT: Keep your response consistent with the above conversation.\n"
        
        # 添加条件触发要求
        if condition_trigger:
            available_conditions = step.get('input_requirements', {}).get('available_conditions', [])
            if available_conditions:
                prompt += f"\nCONDITION REQUIREMENT:\n"
                prompt += f"Your response must trigger: \"{condition_trigger}\"\n"
                prompt += f"Available options: {available_conditions}\n"
                prompt += f"Make sure your response clearly expresses the intent of \"{condition_trigger}\"\n"
            else:
                prompt += f"\nYour response should express: \"{condition_trigger}\"\n"
        
        # 添加节点类型指导
        if node_type == 'info_collection_node':
            prompt += "\nProvide the requested information naturally.\n"
        elif node_type == 'condition_node':
            prompt += "\nRespond in a way that clearly indicates your choice.\n"
        
        prompt += "\nGenerate ONLY the user response (8-20 words), nothing else:"
        
        return prompt
    
    def generate_user_input(self, step: Dict, context: Dict) -> str:
        """生成用户输入"""
        if not self.llm:
            return self.generate_fallback(step)
        
        try:
            prompt = self.create_prompt(step, context)
            response = self.llm.invoke([HumanMessage(content=prompt)])
            user_input = response.content.strip()
            
            # 验证长度
            word_count = len(user_input.split())
            if word_count > 25:
                print(f"  Warning: Generated input too long ({word_count} words), using fallback")
                return self.generate_fallback(step)
            
            return user_input
            
        except Exception as e:
            print(f"  Error generating with LLM: {e}")
            return self.generate_fallback(step)
    
    def generate_fallback(self, step: Dict) -> str:
        """简单的fallback生成"""
        condition = step.get('condition_trigger', '')
        node_type = step['node_type']
        
        if condition:
            # 基于条件生成简单回应
            if 'yes' in condition.lower():
                return "Yes, that's correct."
            elif 'no' in condition.lower():
                return "No, that's not right."
            elif 'not the account owner' in condition.lower():
                return "This is not my account."
            elif 'reset record' in condition.lower():
                return "I tried resetting it before."
            else:
                return f"I need help with {condition.lower()}."
        
        # 基于节点类型的通用回应
        if node_type == 'info_collection_node':
            return "I need help with my 2FA issue."
        else:
            return "I understand."
    
    def add_to_history(self, step: Dict, user_input: str):
        """添加到对话历史"""
        self.conversation_history.append({
            'step': step['step'],
            'user_input': user_input,
            'condition_trigger': step.get('condition_trigger')
        })
    
    def generate_inputs_for_path(self, path_info: Dict) -> Dict:
        """为整个路径生成用户输入"""
        print(f"Generating natural inputs for {path_info['path_id']}")
        print("=" * 60)
        
        self.conversation_history = []  # 重置对话历史
        generated_sequence = []
        
        context = {
            'path_id': path_info['path_id'],
            'total_steps': path_info['total_steps']
        }
        
        for step in path_info['steps']:
            step_num = step['step']
            node_title = step['node_title']
            node_type = step['node_type']

            # 跳过start和intent节点，因为demo.py会跳过这些节点
            if node_type in ['start_node', 'intent_node']:
                print(f"Step {step_num}: {node_type} - {node_title} (跳过)")
                continue

            print(f"Step {step_num}: {node_type} - {node_title}")

            # 生成用户输入
            user_input = self.generate_user_input(step, context)

            print(f"  → Generated: \"{user_input}\"")

            # 添加到历史和结果
            self.add_to_history(step, user_input)
            generated_sequence.append({
                'step': step_num,
                'user_input': user_input
            })
        
        # 构建输出
        result = {
            'path_id': path_info['path_id'],
            'total_steps': len(generated_sequence),
            'description': f"Natural user inputs for {path_info['path_id']}",
            'language': 'English',
            'style': 'Natural and concise',
            'sequence': generated_sequence
        }
        
        return result

def main():
    if len(sys.argv) != 2:
        print("Usage: python generate_natural_inputs_v2.py <path_info_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            path_info = json.load(f)
        
        generator = NaturalInputGenerator()
        result = generator.generate_inputs_for_path(path_info)
        
        # 保存结果
        output_file = f"{path_info['path_id']}_natural_inputs.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n✓ Natural inputs generated and saved to {output_file}")
        print(f"Summary:")
        print(f"  - Total inputs: {len(result['sequence'])}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
