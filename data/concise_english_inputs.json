{"path_id": "path_0013", "total_steps": 20, "description": "19-node SOP path with concise English user inputs", "language": "English", "style": "Concise and natural", "sequence": [{"step": 1, "node_type": "Start", "node_title": "Start", "user_input": null, "expected_response": "Start 2FA reset process"}, {"step": 2, "node_type": "Intent", "node_title": "2FA cannot be used", "user_input": "My phone broke and I can't access my 2FA app. I need to reset it.", "condition_trigger": "2FA cannot be used"}, {"step": 3, "node_type": "Info Collection", "node_title": "User provide or confirm UID to reset 2FA", "user_input": "My UID is *********. Can you reset the 2FA for this account?", "condition_trigger": null}, {"step": 4, "node_type": "Condition", "node_title": "request to reset on the account user logged in or associated to?", "user_input": "I'm not logged in right now because of the 2FA issue.", "condition_trigger": "user didn't login"}, {"step": 5, "node_type": "Condition", "node_title": "Which 2FA User wants to reset? Has reset or not?", "user_input": "I tried resetting it myself yesterday but it didn't work.", "condition_trigger": "has reset record"}, {"step": 6, "node_type": "Condition", "node_title": "User self reset result (updated)", "user_input": "The manual review was rejected for face attack detection issues.", "condition_trigger": "Manual review refuse - MFA_REJECTION_FACE_ATTACK_NON_AI"}, {"step": 7, "node_type": "Info Expression", "node_title": "06. Provide Face Attack disclaimer", "user_input": "I understand. This is really me, not a fake.", "condition_trigger": null}, {"step": 8, "node_type": "Condition", "node_title": "Check if the User recognise the 2FA reset request action", "user_input": "Yes, I made this reset request myself.", "condition_trigger": "YES"}, {"step": 9, "node_type": "Condition", "node_title": "User assets [ HERE ]", "user_input": "I have less than 1 BTC in my account.", "condition_trigger": "Asset < 1 BTC"}, {"step": 10, "node_type": "Condition", "node_title": "Any active block case", "user_input": "No, my account isn't blocked or restricted.", "condition_trigger": "User doesn't has active block case"}, {"step": 11, "node_type": "Condition", "node_title": "User account condition", "user_input": "I have multiple face records saved in my account.", "condition_trigger": "With multiple face record"}, {"step": 12, "node_type": "Condition", "node_title": "Can User recognise the other person?", "user_input": "Yes, I can recognize other people.", "condition_trigger": "User can recognise"}, {"step": 13, "node_type": "Info Expression", "node_title": "19. Collect Multiple Face Statement Video", "user_input": "Okay, I understand I need to provide a multiple face video.", "condition_trigger": null}, {"step": 14, "node_type": "Condition", "node_title": "Can user provide multiple face statement video?", "user_input": "I can't provide that type of video right now.", "condition_trigger": "Cannot"}, {"step": 15, "node_type": "Info Expression", "node_title": "21. Collect Statement Video and First Deposit Video", "user_input": "Got it. I need to provide statement and first deposit videos.", "condition_trigger": null}, {"step": 16, "node_type": "Condition", "node_title": "Can user provide the First Deposit Video?", "user_input": "I can't provide the first deposit video due to other reasons.", "condition_trigger": "ELSE (other cases)"}, {"step": 17, "node_type": "Info Expression", "node_title": "22. Collect alternative materials together with Statement Video or Multiple Face Video", "user_input": "Understood. I'll provide alternative materials with the statement video.", "condition_trigger": null}, {"step": 18, "node_type": "Condition", "node_title": "Can user provide alternative material?", "user_input": "Yes, I can provide alternative verification materials.", "condition_trigger": "YES"}, {"step": 19, "node_type": "Condition", "node_title": "User device", "user_input": "I can't do video calls on my device.", "condition_trigger": "User cannot participate in video call"}, {"step": 20, "node_type": "Info Expression", "node_title": "25. Statement Video", "user_input": "Alright, I'll record the final statement video.", "condition_trigger": null, "is_terminal": true}]}