#!/usr/bin/env python3
"""
Complete Path Data Generator
生成包含完整节点信息的路径数据，包括条件配置和next_nodes映射
"""

import json
from typing import Dict, List, Any

def load_json_file(filename: str) -> Dict:
    """Load JSON file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in {filename}: {e}")
        return {}

def parse_path_description(path_description: str) -> List[Dict]:
    """解析路径描述，提取节点信息"""
    # 移除"Start-->"前缀
    if path_description.startswith("Start-->"):
        path_description = path_description[8:]

    # 按"-->"分割步骤
    steps = path_description.split("-->")

    parsed_steps = []
    for i, step in enumerate(steps):
        step = step.strip()
        if not step:
            continue

        # 解析步骤格式：NodeType:Title
        if ":" in step:
            node_type, title = step.split(":", 1)
            node_type = node_type.strip()
            title = title.strip()
        else:
            node_type = "Unknown"
            title = step

        # 映射节点类型
        type_mapping = {
            'Intent': 'intent_node',
            'Info Collection': 'info_collection_node',
            'Condition': 'condition_node',
            'Info Expression': 'info_expression_node'
        }

        parsed_step = {
            'step': i + 1,  # 从1开始计数
            'node_type': type_mapping.get(node_type, node_type.lower().replace(' ', '_') + '_node'),
            'title': title
        }

        parsed_steps.append(parsed_step)

    return parsed_steps

def find_matching_sop_node(title: str, sop_data: Dict) -> str:
    """根据标题找到匹配的SOP节点ID"""
    nodes_data = sop_data.get('nodes', {})

    # 精确匹配
    for node_id, node_data in nodes_data.items():
        if node_data.get('title', '').strip() == title.strip():
            return node_id

    # 模糊匹配
    title_lower = title.lower()
    for node_id, node_data in nodes_data.items():
        node_title_lower = node_data.get('title', '').lower()
        if title_lower in node_title_lower or node_title_lower in title_lower:
            return node_id

    return None

def extract_path_nodes(parsed_steps: List[Dict], sop_data: Dict) -> List[Dict]:
    """从解析的步骤中提取完整的节点信息"""
    nodes_data = sop_data.get('nodes', {})
    path_nodes = []

    for step in parsed_steps:
        title = step['title']
        node_type = step['node_type']

        # 查找匹配的SOP节点
        node_id = find_matching_sop_node(title, sop_data)

        if not node_id:
            print(f"  Warning: No SOP node found for '{title}'")
            continue

        # 获取完整的SOP节点数据
        sop_node = nodes_data[node_id]

        # 构建完整的节点信息
        node_info = {
            'step': step['step'],
            'node_id': node_id,
            'node_type': node_type,
            'title': title,
            'sop_data': {
                'type': sop_node.get('type'),
                'title': sop_node.get('title'),
                'conditions': sop_node.get('conditions', []),
                'next_nodes': sop_node.get('next_nodes', []),
                'content': sop_node.get('content', ''),
                'original_type': sop_node.get('original_type', '')
            }
        }

        # 如果是条件节点，添加条件映射信息
        if sop_node.get('type') == 'Conditional_LLM':
            conditions = sop_node.get('conditions', [])
            next_nodes = sop_node.get('next_nodes', [])

            condition_mapping = []
            for i, condition in enumerate(conditions):
                mapping = {
                    'index': i,
                    'condition': condition.get('logicOperator', f'Option {i}'),
                    'next_node_id': next_nodes[i] if i < len(next_nodes) else None
                }

                # 获取next_node的信息
                if mapping['next_node_id'] and mapping['next_node_id'] in nodes_data:
                    next_node_data = nodes_data[mapping['next_node_id']]
                    mapping['next_node_info'] = {
                        'title': next_node_data.get('title', ''),
                        'type': next_node_data.get('type', ''),
                        'is_terminal': len(next_node_data.get('next_nodes', [])) == 0
                    }

                condition_mapping.append(mapping)

            node_info['condition_mapping'] = condition_mapping

        path_nodes.append(node_info)

    return path_nodes

def generate_complete_path_data():
    """生成包含完整节点信息的路径数据"""
    print("Loading data files...")
    
    # 加载数据文件
    node_only_paths = load_json_file('sop_paths_extracted_nodes_only.json')
    sop_data = load_json_file('sop_langgraph_data.json')
    
    if not node_only_paths or not sop_data:
        print("Failed to load required data files")
        return
    
    print(f"Loaded {len(node_only_paths['paths'])} paths and SOP data with {len(sop_data['nodes'])} nodes")
    
    complete_paths = []
    
    for path_data in node_only_paths['paths']:
        path_id = path_data['path_id']
        print(f"Processing {path_id}...")
        
        # 提取完整的节点信息
        path_nodes = extract_path_nodes(path_data, sop_data)
        
        # 构建完整的路径数据
        complete_path = {
            'SOP_path_id': path_id,
            'path_signature': path_data.get('path_signature_nodes_only', ''),
            'total_steps': len(path_nodes),
            'nodes': path_nodes,
            'description': generate_path_description(path_nodes)
        }
        
        complete_paths.append(complete_path)
    
    # 保存结果
    output_filename = 'sop_paths_complete_data.json'
    with open(output_filename, 'w', encoding='utf-8') as f:
        json.dump(complete_paths, f, indent=2, ensure_ascii=False)
    
    print(f"\nComplete path data saved to: {output_filename}")
    print(f"Total paths processed: {len(complete_paths)}")
    
    # 显示示例
    if complete_paths:
        show_sample_path(complete_paths[0])

def generate_path_description(path_nodes: List[Dict]) -> str:
    """生成路径描述"""
    description_parts = []
    
    for node in path_nodes:
        step = node['step']
        node_type = node['node_type']
        title = node['title']
        
        # 简化节点类型显示
        type_mapping = {
            'start_node': 'Start',
            'intent_node': 'Intent', 
            'info_collection_node': 'Info Collection',
            'condition_node': 'Condition',
            'info_expression_node': 'Info Expression'
        }
        
        type_display = type_mapping.get(node_type, node_type)
        description_parts.append(f"{step}.{type_display}:{title}")
    
    return "-->".join(description_parts)

def show_sample_path(path_data: Dict):
    """显示示例路径数据"""
    print("\n" + "="*80)
    print("SAMPLE PATH DATA:")
    print("="*80)
    print(f"Path ID: {path_data['SOP_path_id']}")
    print(f"Total Steps: {path_data['total_steps']}")
    print(f"Description: {path_data['description']}")
    print()
    
    # 显示前3个节点的详细信息
    for i, node in enumerate(path_data['nodes'][:3]):
        print(f"Node {i+1}:")
        print(f"  Step: {node['step']}")
        print(f"  Type: {node['node_type']}")
        print(f"  Title: {node['title']}")
        
        if 'condition_mapping' in node:
            print(f"  Conditions:")
            for mapping in node['condition_mapping']:
                terminal_info = ""
                if 'next_node_info' in mapping:
                    terminal_info = " (Terminal)" if mapping['next_node_info']['is_terminal'] else " (Continue)"
                print(f"    {mapping['index']}: {mapping['condition']} → {mapping['next_node_id']}{terminal_info}")
        print()

if __name__ == "__main__":
    generate_complete_path_data()
