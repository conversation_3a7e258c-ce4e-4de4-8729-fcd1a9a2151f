#!/usr/bin/env python3
"""
基于完整节点数据的路径信息提取器
使用sop_paths_node_only_fullinfo.json作为数据源
"""

import json
import sys
from typing import List, Dict, Optional

class PathInfoExtractorV2:
    def __init__(self):
        self.paths_data = None
        self.load_data()
    
    def load_data(self):
        """Load complete path data and SOP data"""
        try:
            with open('sop_paths_extracted_nodes_only.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.paths_data = data.get('paths', [])

            with open('new_2fa_sop.json', 'r', encoding='utf-8') as f:
                raw_sop_data = json.load(f)
                # 转换新格式到旧格式
                self.sop_data = self._convert_sop_data(raw_sop_data)

            print(f"✓ Loaded {len(self.paths_data)} complete paths")

        except FileNotFoundError as e:
            print(f"Error: Missing required file - {e}")
            raise

    def _convert_sop_data(self, raw_sop_data: Dict) -> Dict:
        """转换新SOP数据格式为旧格式，并过滤无效边"""
        nodes_dict = {}

        # 首先收集所有存在的节点ID
        existing_node_ids = set()
        for node in raw_sop_data.get('nodes', []):
            existing_node_ids.add(node['id'])

        print(f"✓ 发现 {len(existing_node_ids)} 个节点")

        # 检查边的有效性
        edges = raw_sop_data.get('edges', [])
        valid_edges = []
        invalid_edges = []

        for edge in edges:
            source = edge.get('source')
            target = edge.get('target')

            # 检查源节点和目标节点是否都存在
            if source in existing_node_ids and target in existing_node_ids:
                valid_edges.append(edge)
            else:
                invalid_edges.append({
                    'source': source,
                    'target': target,
                    'source_exists': source in existing_node_ids,
                    'target_exists': target in existing_node_ids
                })

        if invalid_edges:
            print(f"⚠️  发现 {len(invalid_edges)} 条无效边，将被排除:")
            for invalid_edge in invalid_edges[:5]:  # 只显示前5个
                source_status = "✓" if invalid_edge['source_exists'] else "✗"
                target_status = "✓" if invalid_edge['target_exists'] else "✗"
                print(f"   {source_status} {invalid_edge['source'][:20]}... → {target_status} {invalid_edge['target'][:20]}...")
            if len(invalid_edges) > 5:
                print(f"   ... 还有 {len(invalid_edges) - 5} 条无效边")

        print(f"✓ 保留 {len(valid_edges)} 条有效边")

        # 转换节点数据格式
        for node in raw_sop_data.get('nodes', []):
            node_id = node['id']
            node_data = node.get('data', {})

            # 转换节点数据格式
            converted_node = {
                'type': node_data.get('type', 'unknown'),
                'title': node_data.get('value', {}).get('title', ''),
                'content': node_data.get('value', {}).get('content', ''),
                'conditions': node_data.get('value', {}).get('conditions', [])
            }

            nodes_dict[node_id] = converted_node

        return {
            'nodes': nodes_dict,
            'valid_edges': valid_edges,
            'invalid_edges_count': len(invalid_edges)
        }
    
    def extract_path_info(self, path_id: str) -> Dict:
        """Extract information for a specific path"""
        # Find the path in the data
        path_data = None
        for path in self.paths_data:
            if path['path_id'] == path_id:
                path_data = path
                break
        
        if not path_data:
            print(f"Error: Path {path_id} not found")
            return {}
        
        print(f"Processing path: {path_id}")
        print(f"Description: {path_data['description']}")
        print(f"Total nodes: {len(path_data['steps'])}")
        
        # 处理节点数据，提取用户输入需求
        enriched_steps = []
        
        for step_num, step in enumerate(path_data['steps'], 1):
            # 从SOP数据中获取完整节点信息
            node_id = step['node_id']
            sop_node = self.sop_data['nodes'].get(node_id, {})

            # 构建步骤信息
            step_info = {
                'step': step_num,
                'node_id': node_id,
                'node_type': step['type'],
                'node_title': step['title'],
                'node_description': sop_node.get('description', ''),
                'guidance': sop_node.get('guidance'),
                'conditions': sop_node.get('conditions', []),
                'collection_info': sop_node.get('collection_info', [])
            }
            
            # 确定是否需要用户输入
            needs_user_input = self._needs_user_input(sop_node)
            step_info['needs_user_input'] = needs_user_input

            # 如果需要用户输入，确定输入类型和要求
            if needs_user_input:
                step_info['input_requirements'] = self._determine_input_requirements(sop_node, node_id)

                # 对于条件节点，提取条件触发信息
                if step['type'] == 'condition_node' and sop_node.get('conditions'):
                    # 传递正确的节点ID
                    node_with_id = sop_node.copy()
                    node_with_id['id'] = step['node_id']  # 使用step中的node_id
                    step_info['condition_trigger'] = self._extract_condition_trigger(node_with_id, step_num, path_data)

            # 只包含重要的用户交互节点（排除start和intent节点）
            if step['type'] not in ['start_node', 'intent_node']:
                enriched_steps.append(step_info)
        
        # Build the final path info
        path_info = {
            'path_id': path_id,
            'description': path_data['description'],
            'total_steps': len(enriched_steps),
            'steps': enriched_steps
        }
        
        return path_info
    
    def _needs_user_input(self, node: Dict) -> bool:
        """判断节点是否需要用户输入"""
        node_type = node['type']

        # 明确排除不需要用户输入的节点类型
        excluded_types = [
            'start_node',
            'intent_node',
            'info_expression_node'  # 这些节点只是显示信息，不需要用户输入
        ]

        if node_type in excluded_types:
            return False

        # 这些节点类型需要用户输入
        input_required_types = [
            'info_collection_node',
            'condition_node'
        ]

        return node_type in input_required_types
    
    def _determine_input_requirements(self, node: Dict, node_id: str) -> Dict:
        """确定输入要求，过滤无效的条件选项"""
        node_type = node['type']
        requirements = {
            'input_type': 'general',
            'description': node.get('description', ''),
            'guidance': node.get('guidance'),
            'collection_info': node.get('collection_info', [])
        }

        if node_type == 'info_collection_node':
            requirements['input_type'] = 'information_collection'
            # 从collection_info中提取具体要求
            if node.get('collection_info'):
                requirements['specific_requirements'] = [
                    info.get('name', '') for info in node['collection_info']
                ]

        elif node_type == 'condition_node':
            requirements['input_type'] = 'decision_trigger'
            # 从conditions中提取选项，但只包含指向有效节点的条件
            if node.get('conditions'):
                all_conditions = [
                    cond.get('logicOperator', '') for cond in node['conditions']
                    if cond.get('logicOperator', '').strip()
                ]

                # 过滤掉指向不存在节点的条件
                valid_conditions = self._filter_valid_conditions(node_id, node.get('conditions', []))

                requirements['available_conditions'] = all_conditions
                requirements['valid_conditions'] = valid_conditions

                if len(valid_conditions) < len(all_conditions):
                    print(f"  ⚠️  节点 {node_id[:20]}... 有 {len(all_conditions) - len(valid_conditions)} 个条件指向不存在的节点")

        return requirements

    def _filter_valid_conditions(self, node_id: str, conditions: List[Dict]) -> List[str]:
        """过滤出指向有效节点的条件"""
        valid_conditions = []
        valid_edges = self.sop_data.get('valid_edges', [])

        # 为当前节点构建条件到目标节点的映射
        condition_to_target = {}
        for edge in valid_edges:
            if edge.get('source') == node_id:
                source_handle = edge.get('sourceHandle', '')
                target = edge.get('target', '')
                condition_to_target[source_handle] = target

        # 检查每个条件是否有对应的有效边
        for condition in conditions:
            condition_text = condition.get('logicOperator', '').strip()
            condition_handle = condition.get('handle', '')

            if condition_text and condition_handle in condition_to_target:
                valid_conditions.append(condition_text)

        return valid_conditions

    def _extract_condition_trigger(self, node: Dict, step_num: int, path_data: Dict) -> str:
        """提取条件触发信息 - 从原始路径数据中的condition_choice获取正确的条件"""
        conditions = node.get('conditions', [])

        if not conditions:
            return None

        # 从原始路径数据中查找当前步骤的condition_choice
        current_node_id = node.get('id')
        path_id = path_data.get('path_id')

        # 在完整的paths_data中查找当前路径的详细信息
        full_path_data = None
        for path in self.paths_data:
            if path.get('path_id') == path_id:
                full_path_data = path
                break

        if full_path_data:
            # 在原始路径数据的steps中查找当前节点
            original_steps = full_path_data.get('steps', [])
            for step in original_steps:
                step_node_id = step.get('node_id')
                step_type = step.get('type')
                if step_node_id == current_node_id and step_type == 'condition_node':
                    # 找到了当前条件节点，获取其condition_choice
                    condition_choice = step.get('condition_choice', {})
                    if condition_choice:
                        expected_answer = condition_choice.get('answer', '').strip()
                        if expected_answer:
                            print(f"  ✅ 从路径数据中找到预期条件: \"{expected_answer}\"")
                            return expected_answer

        # 如果没有找到condition_choice，使用第一个非空条件作为fallback
        print(f"  ⚠️  没有找到condition_choice，使用第一个非空条件")
        for condition in conditions:
            logic_op = condition.get('logicOperator', '').strip()
            if logic_op:
                return logic_op

        return conditions[0].get('logicOperator', '') if conditions else None

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python extract_path_info_v2.py <path_id>")
        print("Example: python extract_path_info_v2.py path_0000")
        return
    
    path_id = sys.argv[1]
    
    try:
        extractor = PathInfoExtractorV2()
        path_info = extractor.extract_path_info(path_id)
        
        if path_info:
            # Save the extracted info
            output_filename = f"{path_id}_extracted_info_v2.json"
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(path_info, f, indent=2, ensure_ascii=False)
            
            print(f"\n✓ Path info saved to: {output_filename}")
            
            # Display summary
            print(f"\nPath Summary:")
            print(f"  Path ID: {path_info['path_id']}")
            print(f"  Total Steps: {path_info['total_steps']}")
            print(f"  User Input Steps: {sum(1 for step in path_info['steps'] if step.get('needs_user_input'))}")
            
            print(f"\nSteps requiring user input:")
            for step in path_info['steps']:
                if step.get('needs_user_input'):
                    print(f"  Step {step['step']}: {step['node_type']} - {step['node_title']}")
                    if step.get('condition_trigger'):
                        print(f"    Condition trigger: {step['condition_trigger']}")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    main()
