#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试Fallback机制的脚本
直接跳转到condition_node来测试fallback逻辑
"""

import json
import time
from typing import Dict, List
from sop_core import SimpleSOPConversation, create_llm
from langchain_core.messages import HumanMessage

class DirectFallbackTester:
    """直接测试Fallback机制的类"""
    
    def __init__(self):
        self.llm = create_llm()
        self.test_cases = self._generate_test_cases()
    
    def _generate_test_cases(self) -> List[Dict]:
        """生成测试用例"""
        return [
            # 测试用例1: 清晰的回答 (应该成功)
            {
                "category": "CLEAR",
                "description": "明确回答：是账户所有者",
                "user_input": "Yes, I am the account owner and this is my associated account.",
                "expected_status": "normal"
            },
            {
                "category": "CLEAR", 
                "description": "明确回答：不是账户所有者",
                "user_input": "No, I am not the account owner. This is for my colleague's account.",
                "expected_status": "normal"
            },
            
            # 测试用例2: 模糊的回答 (应该触发AMBIGUOUS fallback)
            {
                "category": "AMBIGUOUS",
                "description": "模糊回答：不确定",
                "user_input": "I'm not sure... maybe?",
                "expected_status": "fallback",
                "expected_reason": "AMBIGUOUS"
            },
            {
                "category": "AMBIGUOUS",
                "description": "模糊回答：可能是",
                "user_input": "I think so, but I need to check.",
                "expected_status": "fallback", 
                "expected_reason": "AMBIGUOUS"
            },
            {
                "category": "AMBIGUOUS",
                "description": "模糊回答：不太清楚",
                "user_input": "Well, it's complicated...",
                "expected_status": "fallback",
                "expected_reason": "AMBIGUOUS"
            },
            
            # 测试用例3: 无关的回答 (应该触发IRRELEVANT fallback)
            {
                "category": "IRRELEVANT",
                "description": "无关回答：问天气", 
                "user_input": "How's the weather today?",
                "expected_status": "fallback",
                "expected_reason": "IRRELEVANT"
            },
            {
                "category": "IRRELEVANT",
                "description": "无关回答：闲聊",
                "user_input": "I had a great lunch today!",
                "expected_status": "fallback",
                "expected_reason": "IRRELEVANT"
            },
            {
                "category": "IRRELEVANT", 
                "description": "无关回答：询问其他问题",
                "user_input": "Can you help me with my password instead?",
                "expected_status": "fallback",
                "expected_reason": "IRRELEVANT"
            },
            
            # 测试用例4: 边界情况
            {
                "category": "EDGE",
                "description": "空回答",
                "user_input": "",
                "expected_status": "fallback",
                "expected_reason": "IRRELEVANT"
            },
            {
                "category": "EDGE",
                "description": "只有标点符号",
                "user_input": "???",
                "expected_status": "fallback",
                "expected_reason": "AMBIGUOUS"
            }
        ]
    
    def find_condition_node(self, conversation: SimpleSOPConversation) -> str:
        """找到第一个条件节点"""
        sop_data = conversation.sop_data
        for node_id, node_config in sop_data['nodes'].items():
            if node_config.get('type') == 'condition_node' and node_config.get('conditions'):
                return node_id
        return None
    
    def setup_condition_node_state(self, conversation: SimpleSOPConversation, condition_node_id: str):
        """设置对话状态到指定的条件节点"""
        # 清空消息历史
        conversation.current_state['messages'] = []
        
        # 设置当前节点为条件节点
        conversation.current_state['current_node'] = condition_node_id
        conversation.current_state['need_user_input'] = True
        conversation.current_state['conversation_ended'] = False
        conversation.current_state['fallback_info'] = None
        
        # 获取节点配置并执行一次，生成问题
        from sop_core import create_node_executor
        node_executor = create_node_executor(condition_node_id)
        updated_state = node_executor(conversation.current_state)
        conversation.current_state.update(updated_state)
    
    def run_single_test(self, test_case: Dict, test_num: int) -> Dict:
        """运行单个测试用例"""
        print(f"\n{'='*60}")
        print(f"测试 #{test_num}: {test_case['category']} - {test_case['description']}")
        print(f"{'='*60}")
        
        try:
            # 初始化对话
            conversation = SimpleSOPConversation()
            conversation.start_conversation()
            
            # 找到条件节点
            condition_node_id = self.find_condition_node(conversation)
            if not condition_node_id:
                print("❌ 无法找到条件节点")
                return {
                    "test_case": test_case,
                    "success": False,
                    "error": "No condition node found",
                    "processing_time": 0
                }
            
            # 设置状态到条件节点
            self.setup_condition_node_state(conversation, condition_node_id)
            
            # 获取生成的问题
            messages = conversation.current_state.get('messages', [])
            if messages and messages[-1]['role'] == 'assistant':
                agent_question = messages[-1]['content']
                print(f"🤖 Agent: {agent_question}")
            else:
                print(f"🤖 Agent: [条件节点问题]")
            
            print(f"👤 User: {test_case['user_input']}")
            
            # 处理用户输入
            start_time = time.time()
            response, step_info = conversation.process_user_input(test_case['user_input'])
            end_time = time.time()
            
            # 分析结果
            actual_status = step_info.get('status', 'unknown')
            fallback_details = step_info.get('fallback_details')
            actual_reason = fallback_details.get('reason') if fallback_details else None
            
            print(f"🤖 Agent: {response}")
            print(f"\n📊 测试结果:")
            print(f"   实际状态: {actual_status}")
            print(f"   期望状态: {test_case['expected_status']}")
            
            # 判断测试是否通过
            success = True
            if actual_status != test_case['expected_status']:
                success = False
                print(f"   ❌ 状态不匹配!")
            
            if test_case['expected_status'] == 'fallback':
                expected_reason = test_case.get('expected_reason')
                if actual_reason != expected_reason:
                    success = False
                    print(f"   ❌ Fallback原因不匹配! 期望: {expected_reason}, 实际: {actual_reason}")
                else:
                    print(f"   ✅ Fallback原因匹配: {actual_reason}")
            
            if success:
                print(f"   ✅ 测试通过!")
            else:
                print(f"   ❌ 测试失败!")
            
            # 显示详细的fallback信息
            if fallback_details:
                print(f"\n🚨 Fallback详情:")
                print(f"   原因: {fallback_details['reason']}")
                print(f"   节点问题: {fallback_details['node_question']}")
                print(f"   期望条件: {fallback_details['expected_conditions']}")
                print(f"   用户输入: {fallback_details['user_input']}")
            
            print(f"\n⏱️ 处理时间: {end_time - start_time:.2f}s")
            
            return {
                "test_case": test_case,
                "success": success,
                "actual_status": actual_status,
                "actual_reason": actual_reason,
                "processing_time": end_time - start_time,
                "fallback_details": fallback_details,
                "condition_node_id": condition_node_id
            }
            
        except Exception as e:
            print(f"❌ 测试执行错误: {e}")
            import traceback
            traceback.print_exc()
            return {
                "test_case": test_case,
                "success": False,
                "error": str(e),
                "processing_time": 0
            }
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 直接Fallback机制测试开始")
        print("="*80)
        print("目标: 直接测试条件节点的Fallback机制")
        print("方法: 跳过前置流程，直接在condition_node测试")
        print("="*80)
        
        # 运行所有测试
        results = []
        for i, test_case in enumerate(self.test_cases, 1):
            result = self.run_single_test(test_case, i)
            results.append(result)
            
            # 避免请求过快
            time.sleep(1)
        
        # 生成测试报告
        self.generate_test_report(results)
    
    def generate_test_report(self, results: List[Dict]):
        """生成测试报告"""
        print(f"\n📋 测试报告")
        print("="*80)
        
        # 统计结果
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        # 按类别统计
        category_stats = {}
        for result in results:
            category = result['test_case']['category']
            if category not in category_stats:
                category_stats[category] = {'total': 0, 'passed': 0}
            category_stats[category]['total'] += 1
            if result.get('success', False):
                category_stats[category]['passed'] += 1
        
        print(f"\n📊 分类统计:")
        for category, stats in category_stats.items():
            success_rate = stats['passed'] / stats['total'] * 100
            print(f"   {category:20s}: {stats['passed']:2d}/{stats['total']:2d} ({success_rate:5.1f}%)")
        
        # 显示失败的测试
        failed_results = [r for r in results if not r.get('success', False)]
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for i, result in enumerate(failed_results, 1):
                test_case = result['test_case']
                print(f"   {i}. {test_case['category']} - {test_case['description']}")
                print(f"      输入: {test_case['user_input']}")
                print(f"      期望: {test_case['expected_status']}")
                print(f"      实际: {result.get('actual_status', 'unknown')}")
                if 'error' in result:
                    print(f"      错误: {result['error']}")
        
        # 性能统计
        processing_times = [r.get('processing_time', 0) for r in results if 'processing_time' in r]
        if processing_times:
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)
            print(f"\n⏱️ 性能统计:")
            print(f"   平均处理时间: {avg_time:.2f}s")
            print(f"   最长处理时间: {max_time:.2f}s")
            print(f"   最短处理时间: {min_time:.2f}s")
        
        # Fallback原因统计
        fallback_reasons = {}
        for result in results:
            fallback_details = result.get('fallback_details')
            if fallback_details:
                reason = fallback_details.get('reason', 'UNKNOWN')
                fallback_reasons[reason] = fallback_reasons.get(reason, 0) + 1
        
        if fallback_reasons:
            print(f"\n🚨 Fallback原因统计:")
            for reason, count in fallback_reasons.items():
                print(f"   {reason}: {count} 次")
        
        # 显示使用的条件节点信息
        condition_nodes = set()
        for result in results:
            if 'condition_node_id' in result:
                condition_nodes.add(result['condition_node_id'])
        
        if condition_nodes:
            print(f"\n🔗 测试的条件节点:")
            for node_id in condition_nodes:
                print(f"   {node_id}")

def main():
    """主函数"""
    print("直接Fallback机制测试工具")
    print("跳过前置流程，直接测试条件节点的Fallback逻辑")
    print()
    
    tester = DirectFallbackTester()
    tester.run_all_tests()
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    main() 