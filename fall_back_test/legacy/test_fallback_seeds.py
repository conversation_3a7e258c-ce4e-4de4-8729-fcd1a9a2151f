#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于种子的Fallback机制测试脚本
使用预定义的种子生成具有明确意图的测试用例
"""

import json
import time
from typing import Dict, List
from sop_core import SimpleSOPConversation, create_llm
from langchain_core.messages import HumanMessage

class SeedBasedFallbackTester:
    """基于种子的Fallback机制测试类"""
    
    def __init__(self):
        self.llm = create_llm()
        self.seeds = self._define_seeds()
    
    def _define_seeds(self) -> Dict[str, List[Dict]]:
        """定义测试种子"""
        return {
            "clear_answers": [
                {
                    "seed": "I remember my password perfectly",
                    "description": "明确表示记得密码",
                    "expected_status": "normal"
                },
                {
                    "seed": "I forgot my password but have my phone for 2FA",
                    "description": "明确表示忘记密码但有2FA",
                    "expected_status": "normal"
                },
                {
                    "seed": "I lost both my password and my 2FA device",
                    "description": "明确表示两者都丢失",
                    "expected_status": "normal"
                }
            ],
            "unhandled_ambiguous": [
                {
                    "seed": "I'm not sure, maybe I remember",
                    "description": "模糊不确定的回答",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                },
                {
                    "seed": "It depends on what you mean by password",
                    "description": "概念混淆的回答",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                },
                {
                    "seed": "Both options seem to apply to me",
                    "description": "选项混淆的回答",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                }
            ],
            "unhandled_irrelevant": [
                {
                    "seed": "What's the weather like today?",
                    "description": "完全无关的闲聊",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                },
                {
                    "seed": "Can you help me reset my email instead?",
                    "description": "错位的请求",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                },
                {
                    "seed": "Why is your system so complicated?",
                    "description": "抱怨/质疑",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                }
            ],
            "unhandled_edge_cases": [
                {
                    "seed": "",
                    "description": "空输入",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                },
                {
                    "seed": "???",
                    "description": "纯标点符号",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                },
                {
                    "seed": "asdfghjkl",
                    "description": "随机字符",
                    "expected_status": "fallback",
                    "expected_reason": "UNHANDLED"
                }
            ]
        }
    
    def generate_test_cases_from_seeds(self) -> List[Dict]:
        """基于种子生成测试用例"""
        test_cases = []
        
        for category, seeds in self.seeds.items():
            for seed_data in seeds:
                # 对于清晰回答，直接使用种子
                if category == "clear_answers":
                    test_cases.append({
                        "category": "CLEAR",
                        "description": seed_data["description"],
                        "user_input": seed_data["seed"],
                        "expected_status": seed_data["expected_status"],
                        "seed_origin": seed_data["seed"]
                    })
                else:
                    # 对于需要fallback的情况，生成变体
                    variations = self._generate_variations(seed_data["seed"], category)
                    for i, variation in enumerate(variations):
                        test_cases.append({
                            "category": "UNHANDLED",
                            "description": f"{seed_data['description']} - 变体{i+1}",
                            "user_input": variation,
                            "expected_status": seed_data["expected_status"],
                            "expected_reason": seed_data["expected_reason"],
                            "seed_origin": seed_data["seed"]
                        })
        
        return test_cases
    
    def _generate_variations(self, seed: str, category: str) -> List[str]:
        """基于种子生成变体"""
        if not self.llm or not seed.strip():
            return [seed]  # 空种子直接返回
        
        try:
            if "ambiguous" in category:
                prompt = f"""Generate 2 different ways to express the same ambiguous/unclear intent as: "{seed}"
Keep the core uncertainty but use different words. Each response should be 5-15 words.
Format as JSON array: ["variation1", "variation2"]"""
            elif "irrelevant" in category:
                prompt = f"""Generate 2 different off-topic responses similar to: "{seed}"
Keep them unrelated to passwords/2FA but use different topics. Each should be 5-15 words.
Format as JSON array: ["variation1", "variation2"]"""
            elif "edge" in category:
                # 边界情况不生成变体，直接返回原种子
                return [seed]
            else:
                return [seed]
            
            response = self.llm.invoke([HumanMessage(content=prompt)])
            variations_json = response.content.strip()
            
            # 解析JSON
            variations = json.loads(variations_json)
            if isinstance(variations, list):
                return [seed] + variations  # 包含原种子
            else:
                return [seed]
                
        except Exception as e:
            print(f"⚠️ 生成变体失败 ({seed}): {e}")
            return [seed]
    
    def find_condition_node(self, conversation: SimpleSOPConversation) -> str:
        """找到第一个条件节点"""
        sop_data = conversation.sop_data
        for node_id, node_config in sop_data['nodes'].items():
            if node_config.get('type') == 'condition_node' and node_config.get('conditions'):
                return node_id
        return None
    
    def setup_condition_node_state(self, conversation: SimpleSOPConversation, condition_node_id: str):
        """设置对话状态到指定的条件节点"""
        # 清空消息历史
        conversation.current_state['messages'] = []
        
        # 设置当前节点为条件节点
        conversation.current_state['current_node'] = condition_node_id
        conversation.current_state['need_user_input'] = True
        conversation.current_state['conversation_ended'] = False
        conversation.current_state['fallback_info'] = None
        
        # 获取节点配置并执行一次，生成问题
        from sop_core import create_node_executor
        node_executor = create_node_executor(condition_node_id)
        updated_state = node_executor(conversation.current_state)
        conversation.current_state.update(updated_state)
    
    def run_single_test(self, test_case: Dict, test_num: int) -> Dict:
        """运行单个测试用例"""
        print(f"\n{'='*60}")
        print(f"测试 #{test_num}: {test_case['category']} - {test_case['description']}")
        if 'seed_origin' in test_case:
            print(f"种子: {test_case['seed_origin']}")
        print(f"{'='*60}")
        
        try:
            # 初始化对话
            conversation = SimpleSOPConversation()
            conversation.start_conversation()
            
            # 找到条件节点
            condition_node_id = self.find_condition_node(conversation)
            if not condition_node_id:
                print("❌ 无法找到条件节点")
                return {
                    "test_case": test_case,
                    "success": False,
                    "error": "No condition node found",
                    "processing_time": 0
                }
            
            # 设置状态到条件节点
            self.setup_condition_node_state(conversation, condition_node_id)
            
            # 获取生成的问题
            messages = conversation.current_state.get('messages', [])
            if messages and messages[-1]['role'] == 'assistant':
                agent_question = messages[-1]['content']
                print(f"🤖 Agent: {agent_question[:100]}...")
            else:
                print(f"🤖 Agent: [条件节点问题]")
            
            print(f"👤 User: {test_case['user_input']}")
            
            # 处理用户输入
            start_time = time.time()
            response, step_info = conversation.process_user_input(test_case['user_input'])
            end_time = time.time()
            
            # 分析结果
            actual_status = step_info.get('status', 'unknown')
            fallback_details = step_info.get('fallback_details')
            actual_reason = fallback_details.get('reason') if fallback_details else None
            
            print(f"🤖 Agent: {response[:100]}...")
            print(f"\n📊 测试结果:")
            print(f"   实际状态: {actual_status}")
            print(f"   期望状态: {test_case['expected_status']}")
            
            # 判断测试是否通过
            success = True
            if actual_status != test_case['expected_status']:
                success = False
                print(f"   ❌ 状态不匹配!")
            
            if test_case['expected_status'] == 'fallback':
                expected_reason = test_case.get('expected_reason')
                if actual_reason != expected_reason:
                    success = False
                    print(f"   ❌ Fallback原因不匹配! 期望: {expected_reason}, 实际: {actual_reason}")
                else:
                    print(f"   ✅ Fallback原因匹配: {actual_reason}")
            
            if success:
                print(f"   ✅ 测试通过!")
            else:
                print(f"   ❌ 测试失败!")
            
            # 显示详细的fallback信息
            if fallback_details:
                print(f"\n🚨 Fallback详情:")
                print(f"   原因: {fallback_details['reason']}")
                print(f"   节点问题: {fallback_details['node_question'][:50]}...")
                print(f"   用户输入: {fallback_details['user_input']}")
            
            print(f"\n⏱️ 处理时间: {end_time - start_time:.2f}s")
            
            return {
                "test_case": test_case,
                "success": success,
                "actual_status": actual_status,
                "actual_reason": actual_reason,
                "processing_time": end_time - start_time,
                "fallback_details": fallback_details,
                "condition_node_id": condition_node_id
            }
            
        except Exception as e:
            print(f"❌ 测试执行错误: {e}")
            import traceback
            traceback.print_exc()
            return {
                "test_case": test_case,
                "success": False,
                "error": str(e),
                "processing_time": 0
            }
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 基于种子的Fallback机制测试开始")
        print("="*80)
        print("目标: 使用预定义种子验证简化的Fallback机制")
        print("分类: CLEAR (正常路由) vs UNHANDLED (统一fallback)")
        print("="*80)
        
        # 生成测试用例
        print("\n📝 从种子生成测试用例...")
        test_cases = self.generate_test_cases_from_seeds()
        print(f"✅ 总共生成 {len(test_cases)} 个测试用例")
        
        # 显示种子统计
        seed_stats = {}
        for case in test_cases:
            category = case['category']
            seed_stats[category] = seed_stats.get(category, 0) + 1
        
        print(f"\n📊 测试用例分布:")
        for category, count in seed_stats.items():
            print(f"   {category}: {count} 个")
        
        # 运行所有测试
        results = []
        for i, test_case in enumerate(test_cases, 1):
            result = self.run_single_test(test_case, i)
            results.append(result)
            
            # 避免请求过快
            time.sleep(0.5)
        
        # 生成测试报告
        self.generate_test_report(results)
    
    def generate_test_report(self, results: List[Dict]):
        """生成测试报告"""
        print(f"\n📋 测试报告")
        print("="*80)
        
        # 统计结果
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        # 按类别统计
        category_stats = {}
        for result in results:
            category = result['test_case']['category']
            if category not in category_stats:
                category_stats[category] = {'total': 0, 'passed': 0}
            category_stats[category]['total'] += 1
            if result.get('success', False):
                category_stats[category]['passed'] += 1
        
        print(f"\n📊 分类统计:")
        for category, stats in category_stats.items():
            success_rate = stats['passed'] / stats['total'] * 100
            print(f"   {category:15s}: {stats['passed']:2d}/{stats['total']:2d} ({success_rate:5.1f}%)")
        
        # 显示失败的测试
        failed_results = [r for r in results if not r.get('success', False)]
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for i, result in enumerate(failed_results[:5], 1):  # 只显示前5个
                test_case = result['test_case']
                print(f"   {i}. {test_case['description']}")
                print(f"      输入: {test_case['user_input'][:50]}...")
                print(f"      期望: {test_case['expected_status']}")
                print(f"      实际: {result.get('actual_status', 'unknown')}")
                if 'error' in result:
                    print(f"      错误: {result['error']}")
            
            if len(failed_results) > 5:
                print(f"   ... 还有 {len(failed_results) - 5} 个失败测试")
        
        # 性能统计
        processing_times = [r.get('processing_time', 0) for r in results if 'processing_time' in r]
        if processing_times:
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)
            print(f"\n⏱️ 性能统计:")
            print(f"   平均处理时间: {avg_time:.2f}s")
            print(f"   最长处理时间: {max_time:.2f}s")
            print(f"   最短处理时间: {min_time:.2f}s")
        
        # Fallback原因统计
        fallback_reasons = {}
        for result in results:
            fallback_details = result.get('fallback_details')
            if fallback_details:
                reason = fallback_details.get('reason', 'UNKNOWN')
                fallback_reasons[reason] = fallback_reasons.get(reason, 0) + 1
        
        if fallback_reasons:
            print(f"\n🚨 Fallback原因统计:")
            for reason, count in fallback_reasons.items():
                print(f"   {reason}: {count} 次")

def main():
    """主函数"""
    print("基于种子的Fallback机制测试工具")
    print("使用预定义种子验证简化的UNHANDLED fallback逻辑")
    print()
    
    tester = SeedBasedFallbackTester()
    tester.run_all_tests()
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    main() 