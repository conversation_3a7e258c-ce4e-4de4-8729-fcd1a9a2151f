#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fallback机制测试脚本
测试各种导致fallback的用户输入类型
"""

import json
import time
from typing import Dict, List
from sop_core import SimpleSOPConversation, create_llm
from langchain_core.messages import HumanMessage

class FallbackTester:
    """Fallback机制测试类"""
    
    def __init__(self):
        self.llm = create_llm()
        self.test_cases = self._generate_test_cases()
    
    def _generate_test_cases(self) -> List[Dict]:
        """生成测试用例"""
        return [
            # 测试用例1: 清晰的回答 (应该成功)
            {
                "category": "CLEAR",
                "description": "明确回答：是账户所有者",
                "user_input": "Yes, this is my account and I am the owner.",
                "expected_status": "normal"
            },
            {
                "category": "CLEAR", 
                "description": "明确回答：不是账户所有者",
                "user_input": "No, this is not my account. It belongs to my colleague.",
                "expected_status": "normal"
            },
            
            # 测试用例2: 模糊的回答 (应该触发AMBIGUOUS fallback)
            {
                "category": "AMBIGUOUS",
                "description": "模糊回答：不确定",
                "user_input": "I'm not sure... maybe?",
                "expected_status": "fallback",
                "expected_reason": "AMBIGUOUS"
            },
            {
                "category": "AMBIGUOUS",
                "description": "模糊回答：可能是",
                "user_input": "I think so, but I need to check.",
                "expected_status": "fallback", 
                "expected_reason": "AMBIGUOUS"
            },
            {
                "category": "AMBIGUOUS",
                "description": "模糊回答：不太清楚",
                "user_input": "Well, it's complicated...",
                "expected_status": "fallback",
                "expected_reason": "AMBIGUOUS"
            },
            
            # 测试用例3: 无关的回答 (应该触发IRRELEVANT fallback)
            {
                "category": "IRRELEVANT",
                "description": "无关回答：问天气", 
                "user_input": "How's the weather today?",
                "expected_status": "fallback",
                "expected_reason": "IRRELEVANT"
            },
            {
                "category": "IRRELEVANT",
                "description": "无关回答：闲聊",
                "user_input": "I had a great lunch today!",
                "expected_status": "fallback",
                "expected_reason": "IRRELEVANT"
            },
            {
                "category": "IRRELEVANT", 
                "description": "无关回答：询问其他问题",
                "user_input": "Can you help me with my password instead?",
                "expected_status": "fallback",
                "expected_reason": "IRRELEVANT"
            },
            
            # 测试用例4: 边界情况
            {
                "category": "EDGE",
                "description": "空回答",
                "user_input": "",
                "expected_status": "fallback",
                "expected_reason": "IRRELEVANT"
            },
            {
                "category": "EDGE",
                "description": "只有标点符号",
                "user_input": "???",
                "expected_status": "fallback",
                "expected_reason": "AMBIGUOUS"
            }
        ]
    
    def generate_dynamic_test_cases(self) -> List[Dict]:
        """动态生成更多测试用例"""
        if not self.llm:
            return []
        
        dynamic_cases = []
        
        # 生成模糊回答
        ambiguous_prompts = [
            "Generate a vague, unclear response to the question 'Are you the owner of this account?' (8-15 words)",
            "Create an ambiguous answer about account ownership that doesn't clearly say yes or no (10-20 words)",
            "Write a confusing response to account ownership question that would be hard to categorize (8-15 words)"
        ]
        
        # 生成无关回答
        irrelevant_prompts = [
            "Generate a completely off-topic response that ignores the account ownership question (8-15 words)",
            "Create a random statement about something unrelated to 2FA or accounts (8-15 words)", 
            "Write a response that asks about a completely different topic (8-15 words)"
        ]
        
        try:
            # 生成模糊回答
            for i, prompt in enumerate(ambiguous_prompts):
                response = self.llm.invoke([HumanMessage(content=prompt)])
                user_input = response.content.strip()
                dynamic_cases.append({
                    "category": "AMBIGUOUS_DYNAMIC",
                    "description": f"动态生成模糊回答 #{i+1}",
                    "user_input": user_input,
                    "expected_status": "fallback",
                    "expected_reason": "AMBIGUOUS"
                })
            
            # 生成无关回答
            for i, prompt in enumerate(irrelevant_prompts):
                response = self.llm.invoke([HumanMessage(content=prompt)])
                user_input = response.content.strip()
                dynamic_cases.append({
                    "category": "IRRELEVANT_DYNAMIC", 
                    "description": f"动态生成无关回答 #{i+1}",
                    "user_input": user_input,
                    "expected_status": "fallback",
                    "expected_reason": "IRRELEVANT"
                })
                
        except Exception as e:
            print(f"⚠️ 动态生成测试用例失败: {e}")
        
        return dynamic_cases
    
    def run_single_test(self, test_case: Dict, test_num: int) -> Dict:
        """运行单个测试用例"""
        print(f"\n{'='*60}")
        print(f"测试 #{test_num}: {test_case['category']} - {test_case['description']}")
        print(f"{'='*60}")
        
        try:
            # 初始化对话
            conversation = SimpleSOPConversation()
            assistant_message, session_info = conversation.start_conversation()
            
            print(f"🤖 Agent: {assistant_message}")
            print(f"👤 User: {test_case['user_input']}")
            
            # 处理用户输入
            start_time = time.time()
            response, step_info = conversation.process_user_input(test_case['user_input'])
            end_time = time.time()
            
            # 分析结果
            actual_status = step_info.get('status', 'unknown')
            fallback_details = step_info.get('fallback_details')
            actual_reason = fallback_details.get('reason') if fallback_details else None
            
            print(f"🤖 Agent: {response}")
            print(f"\n📊 测试结果:")
            print(f"   实际状态: {actual_status}")
            print(f"   期望状态: {test_case['expected_status']}")
            
            # 判断测试是否通过
            success = True
            if actual_status != test_case['expected_status']:
                success = False
                print(f"   ❌ 状态不匹配!")
            
            if test_case['expected_status'] == 'fallback':
                expected_reason = test_case.get('expected_reason')
                if actual_reason != expected_reason:
                    success = False
                    print(f"   ❌ Fallback原因不匹配! 期望: {expected_reason}, 实际: {actual_reason}")
                else:
                    print(f"   ✅ Fallback原因匹配: {actual_reason}")
            
            if success:
                print(f"   ✅ 测试通过!")
            else:
                print(f"   ❌ 测试失败!")
            
            # 显示详细的fallback信息
            if fallback_details:
                print(f"\n🚨 Fallback详情:")
                print(f"   原因: {fallback_details['reason']}")
                print(f"   节点问题: {fallback_details['node_question']}")
                print(f"   期望条件: {fallback_details['expected_conditions']}")
                print(f"   用户输入: {fallback_details['user_input']}")
            
            print(f"\n⏱️ 处理时间: {end_time - start_time:.2f}s")
            
            return {
                "test_case": test_case,
                "success": success,
                "actual_status": actual_status,
                "actual_reason": actual_reason,
                "processing_time": end_time - start_time,
                "fallback_details": fallback_details
            }
            
        except Exception as e:
            print(f"❌ 测试执行错误: {e}")
            import traceback
            traceback.print_exc()
            return {
                "test_case": test_case,
                "success": False,
                "error": str(e),
                "processing_time": 0
            }
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 Fallback机制测试开始")
        print("="*80)
        print("目标: 验证新的基于LLM自我评估的Fallback机制")
        print("测试类型: CLEAR, AMBIGUOUS, IRRELEVANT, EDGE, DYNAMIC")
        print("="*80)
        
        # 合并静态和动态测试用例
        all_test_cases = self.test_cases.copy()
        
        print("\n📝 生成动态测试用例...")
        dynamic_cases = self.generate_dynamic_test_cases()
        all_test_cases.extend(dynamic_cases)
        
        print(f"✅ 总共 {len(all_test_cases)} 个测试用例")
        
        # 运行所有测试
        results = []
        for i, test_case in enumerate(all_test_cases, 1):
            result = self.run_single_test(test_case, i)
            results.append(result)
            
            # 避免请求过快
            time.sleep(1)
        
        # 生成测试报告
        self.generate_test_report(results)
    
    def generate_test_report(self, results: List[Dict]):
        """生成测试报告"""
        print(f"\n📋 测试报告")
        print("="*80)
        
        # 统计结果
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        # 按类别统计
        category_stats = {}
        for result in results:
            category = result['test_case']['category']
            if category not in category_stats:
                category_stats[category] = {'total': 0, 'passed': 0}
            category_stats[category]['total'] += 1
            if result.get('success', False):
                category_stats[category]['passed'] += 1
        
        print(f"\n📊 分类统计:")
        for category, stats in category_stats.items():
            success_rate = stats['passed'] / stats['total'] * 100
            print(f"   {category:20s}: {stats['passed']:2d}/{stats['total']:2d} ({success_rate:5.1f}%)")
        
        # 显示失败的测试
        failed_results = [r for r in results if not r.get('success', False)]
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for i, result in enumerate(failed_results, 1):
                test_case = result['test_case']
                print(f"   {i}. {test_case['category']} - {test_case['description']}")
                print(f"      输入: {test_case['user_input']}")
                print(f"      期望: {test_case['expected_status']}")
                print(f"      实际: {result.get('actual_status', 'unknown')}")
                if 'error' in result:
                    print(f"      错误: {result['error']}")
        
        # 性能统计
        processing_times = [r.get('processing_time', 0) for r in results if 'processing_time' in r]
        if processing_times:
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)
            print(f"\n⏱️ 性能统计:")
            print(f"   平均处理时间: {avg_time:.2f}s")
            print(f"   最长处理时间: {max_time:.2f}s")
            print(f"   最短处理时间: {min_time:.2f}s")
        
        # Fallback原因统计
        fallback_reasons = {}
        for result in results:
            fallback_details = result.get('fallback_details')
            if fallback_details:
                reason = fallback_details.get('reason', 'UNKNOWN')
                fallback_reasons[reason] = fallback_reasons.get(reason, 0) + 1
        
        if fallback_reasons:
            print(f"\n🚨 Fallback原因统计:")
            for reason, count in fallback_reasons.items():
                print(f"   {reason}: {count} 次")

def main():
    """主函数"""
    print("Fallback机制测试工具")
    print("测试基于LLM自我评估的新Fallback逻辑")
    print()
    
    tester = FallbackTester()
    tester.run_all_tests()
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    main() 