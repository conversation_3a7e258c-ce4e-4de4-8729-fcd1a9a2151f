{"python.defaultInterpreterPath": "python3", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=100"], "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "off", "files.associations": {"*.py": "python", "*.json": "json"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "python.testing.pytestArgs": ["."], "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.pytest_cache": true}, "search.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/node_modules": true, "**/.venv": true}, "files.watcherExclude": {"**/__pycache__/**": true, "**/.pytest_cache/**": true}, "window.zoomLevel": 1.1, "github.copilot.enable": false, "editor.inlineSuggest.enabled": false, "editor.fontSize": 13}