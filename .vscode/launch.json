{"version": "0.2.0", "configurations": [{"name": "🎬 Debug Demo (主演示)", "type": "python", "request": "launch", "program": "${workspaceFolder}/demo.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": true, "stopOnEntry": false}, {"name": "🧪 Debug LangSmith Test (追踪测试)", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_langsmith.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": true, "stopOnEntry": false}, {"name": "🐛 Debug Test (调试功能测试)", "type": "python", "request": "launch", "program": "${workspaceFolder}/debug_test.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": true, "stopOnEntry": false}, {"name": "🔄 Debug SOP Converter (数据转换器)", "type": "python", "request": "launch", "program": "${workspaceFolder}/convert_sop_json.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": true, "stopOnEntry": false}, {"name": "🔧 Debug SOP Core (核心功能测试)", "type": "python", "request": "launch", "program": "${workspaceFolder}/sop_core.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": true, "stopOnEntry": false, "args": []}, {"name": "🧪 Debug API Compatibility Test (API兼容性测试)", "type": "python", "request": "launch", "program": "${workspaceFolder}/测试公司API兼容性.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": true, "stopOnEntry": false}, {"name": "🎬 Debug Demo (Step Mode) - 单步模式", "type": "python", "request": "launch", "program": "${workspaceFolder}/demo.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": true, "args": []}, {"name": "🔍 Debug Current File (当前文件)", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": true, "stopOnEntry": false}, {"name": "🧠 Debug SOP with Custom Input (自定义输入)", "type": "python", "request": "launch", "program": "${workspaceFolder}/demo.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "DEBUG_MODE": "1", "CUSTOM_USER_INPUT": "My account is locked and I need help"}, "justMyCode": true, "stopOnEntry": false}]}