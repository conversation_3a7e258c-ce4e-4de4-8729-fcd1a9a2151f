{"version": "2.0.0", "tasks": [{"label": "🎬 Run Demo", "type": "shell", "command": "python3", "args": ["demo.py"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🧪 Run LangSmith Test", "type": "shell", "command": "python3", "args": ["test_langsmith.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🔄 Convert SOP Data", "type": "shell", "command": "python3", "args": ["convert_sop_json.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🧪 Test API Compatibility", "type": "shell", "command": "python3", "args": ["测试公司API兼容性.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "📦 Install Dependencies", "type": "shell", "command": "pip3", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🧹 Clean Python Cache", "type": "shell", "command": "find", "args": [".", "-type", "d", "-name", "__pycache__", "-exec", "rm", "-rf", "{}", "+"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "📊 Show Project Structure", "type": "shell", "command": "ls", "args": ["-la"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🔍 Check Python Environment", "type": "shell", "command": "python3", "args": ["--version"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}]}